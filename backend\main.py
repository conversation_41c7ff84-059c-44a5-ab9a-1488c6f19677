from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import json
from pathlib import Path
import logging
from fastapi import Query, Body
import random
import sys
import math
from datetime import datetime

logging.basicConfig(stream=sys.stdout, level=logging.DEBUG, format='%(levelname)s:%(message)s')

app = FastAPI()

# Enable CORS for all origins (development only)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Enhanced Data Models for Complex Game Systems
class Resource(BaseModel):
    name: str
    base_value: float
    current_price: float
    demand: float
    supply: float
    volatility: float = 0.1

class TradeRoute(BaseModel):
    from_province: str
    to_province: str
    resource: str
    volume: float
    efficiency: float = 1.0
    established_turn: int

class PopGroup(BaseModel):
    culture: str
    religion: str
    social_class: str  # aristocrat, bourgeois, artisan, farmer, laborer
    profession: str
    size: int
    wealth: float
    happiness: float
    militancy: float
    consciousness: float

class Province(BaseModel):
    name: str
    owner: str
    terrain: str  # plains, hills, mountains, coast, etc.
    climate: str  # temperate, tropical, arid, etc.
    development: float  # 0-100 scale
    population_groups: List[PopGroup] = []
    resources: Dict[str, float] = {}  # resource_name -> quantity
    buildings: Dict[str, int] = {}  # building_type -> count
    trade_routes: List[TradeRoute] = []
    tax_efficiency: float = 0.5
    unrest: float = 0.0

class Technology(BaseModel):
    name: str
    category: str  # military, economic, social, administrative
    cost: int
    prerequisites: List[str] = []
    effects: Dict[str, float] = {}  # effect_name -> modifier
    description: str

class Country(BaseModel):
    name: str
    tag: str  # 3-letter country code
    government_type: str
    ruler_name: str
    capital: str
    provinces: List[str] = []

    # Economic
    treasury: float = 1000.0
    monthly_income: float = 0.0
    monthly_expenses: float = 0.0
    inflation: float = 0.0
    trade_efficiency: float = 0.5

    # Military
    army_size: int = 0
    navy_size: int = 0
    military_tradition: float = 0.0

    # Technology & Research
    technologies: List[str] = []
    research_points: Dict[str, float] = {"military": 0, "economic": 0, "social": 0, "administrative": 0}
    current_research: Dict[str, str] = {}  # category -> tech_name

    # Diplomacy
    prestige: float = 0.0
    diplomatic_reputation: float = 0.0
    relations: Dict[str, float] = {}  # country_tag -> relation_value

    # Social
    stability: float = 50.0
    war_exhaustion: float = 0.0
    legitimacy: float = 100.0

    # Modifiers
    national_modifiers: Dict[str, float] = {}

    geometry: dict  # GeoJSON geometry

# --- Enhanced Core Systems ---
class MapManager:
    def __init__(self):
        logging.debug("Initializing MapManager...")
        self.provinces = self._initialize_provinces()
        self.countries = self.load_countries()
        self._setup_initial_game_state()
        logging.debug(f"MapManager initialized with {len(self.provinces)} provinces and {len(self.countries)} countries")

    def _initialize_provinces(self):
        """Initialize provinces with rich game data"""
        provinces = []

        # Create sample provinces with detailed data
        sample_provinces = [
            {
                "name": "Capitalia", "owner": "Solarian Empire", "terrain": "plains", "climate": "temperate",
                "development": 75.0, "tax_efficiency": 0.8, "unrest": 0.0,
                "resources": {"grain": 3.0, "iron": 1.0, "textiles": 2.0},
                "buildings": {"farm": 2, "textile_mill": 1}
            },
            {
                "name": "Eastmarch", "owner": "Azure League", "terrain": "hills", "climate": "temperate",
                "development": 60.0, "tax_efficiency": 0.7, "unrest": 5.0,
                "resources": {"iron": 2.0, "coal": 1.5, "timber": 1.0},
                "buildings": {"mine": 1, "lumber_mill": 1}
            },
            {
                "name": "Westvale", "owner": "Stormlands", "terrain": "coast", "climate": "temperate",
                "development": 55.0, "tax_efficiency": 0.6, "unrest": 2.0,
                "resources": {"fish": 2.5, "grain": 1.5, "timber": 1.0},
                "buildings": {"fishing_port": 2, "farm": 1}
            },
            {
                "name": "Goldenhills", "owner": "Merchant Republic", "terrain": "mountains", "climate": "arid",
                "development": 45.0, "tax_efficiency": 0.9, "unrest": 0.0,
                "resources": {"precious_metals": 1.0, "iron": 1.5},
                "buildings": {"mine": 2}
            },
            {
                "name": "Greenlands", "owner": "Free Cities", "terrain": "plains", "climate": "tropical",
                "development": 40.0, "tax_efficiency": 0.5, "unrest": 8.0,
                "resources": {"grain": 4.0, "luxury_goods": 0.5},
                "buildings": {"farm": 3}
            }
        ]

        for province_data in sample_provinces:
            # Create population groups for each province
            pop_groups = self._create_population_groups(province_data)

            province = Province(
                name=province_data["name"],
                owner=province_data["owner"],
                terrain=province_data["terrain"],
                climate=province_data["climate"],
                development=province_data["development"],
                population_groups=pop_groups,
                resources=province_data["resources"],
                buildings=province_data["buildings"],
                trade_routes=[],
                tax_efficiency=province_data["tax_efficiency"],
                unrest=province_data["unrest"]
            )
            provinces.append(province)

        return provinces

    def _create_population_groups(self, province_data):
        """Create diverse population groups for a province"""
        pop_groups = []
        base_population = 50000 + random.randint(-20000, 30000)

        # Create different social classes
        class_distributions = {
            "aristocrat": 0.02,
            "bourgeois": 0.08,
            "artisan": 0.15,
            "farmer": 0.45,
            "laborer": 0.30
        }

        cultures = ["imperial", "highland", "coastal", "nomadic", "merchant"]
        religions = ["orthodox", "reformed", "traditional", "mystical"]

        for social_class, percentage in class_distributions.items():
            pop_size = int(base_population * percentage)
            if pop_size > 0:
                pop_group = PopGroup(
                    culture=random.choice(cultures),
                    religion=random.choice(religions),
                    social_class=social_class,
                    profession=self._get_profession_for_class(social_class),
                    size=pop_size,
                    wealth=self._get_base_wealth_for_class(social_class),
                    happiness=random.uniform(4.0, 7.0),
                    militancy=random.uniform(0.0, 2.0),
                    consciousness=random.uniform(2.0, 6.0)
                )
                pop_groups.append(pop_group)

        return pop_groups

    def _get_profession_for_class(self, social_class):
        """Get appropriate profession for social class"""
        professions = {
            "aristocrat": ["noble", "landowner", "officer"],
            "bourgeois": ["merchant", "banker", "industrialist"],
            "artisan": ["craftsman", "shopkeeper", "skilled_worker"],
            "farmer": ["farmer", "herder", "agricultural_worker"],
            "laborer": ["factory_worker", "miner", "dock_worker"]
        }
        return random.choice(professions.get(social_class, ["worker"]))

    def _get_base_wealth_for_class(self, social_class):
        """Get base wealth level for social class"""
        wealth_ranges = {
            "aristocrat": (40, 80),
            "bourgeois": (20, 40),
            "artisan": (8, 20),
            "farmer": (3, 8),
            "laborer": (1, 5)
        }
        min_wealth, max_wealth = wealth_ranges.get(social_class, (1, 5))
        return random.uniform(min_wealth, max_wealth)

    def _setup_initial_game_state(self):
        """Setup initial trade routes and relationships between provinces"""
        # Create some initial trade routes
        if len(self.provinces) >= 3:
            # Trade route from Capitalia to Eastmarch (textiles for iron)
            trade_route = TradeRoute(
                from_province="Capitalia",
                to_province="Eastmarch",
                resource="textiles",
                volume=1.0,
                efficiency=0.8,
                established_turn=1
            )

            # Find and add to appropriate province
            for province in self.provinces:
                if province.name == "Capitalia":
                    province.trade_routes.append(trade_route)
                    break

    def load_countries(self):
        """Load countries from GeoJSON and enhance with game data"""
        geojson_path = Path(__file__).parent / "data" / "countries.geojson"
        logging.debug(f"Loading countries from: {geojson_path}")

        # Create enhanced countries based on provinces
        countries = []
        unique_owners = set(province.owner for province in self.provinces)

        # Create detailed country data
        country_templates = {
            "Solarian Empire": {
                "tag": "SOL", "government_type": "absolute_monarchy", "ruler_name": "Emperor Marcus III",
                "treasury": 2000.0, "prestige": 75.0, "legitimacy": 85.0
            },
            "Azure League": {
                "tag": "AZU", "government_type": "merchant_republic", "ruler_name": "Doge Alessandro",
                "treasury": 1500.0, "prestige": 60.0, "legitimacy": 70.0
            },
            "Stormlands": {
                "tag": "STO", "government_type": "feudal_monarchy", "ruler_name": "Jarl Erikson",
                "treasury": 800.0, "prestige": 45.0, "legitimacy": 90.0
            },
            "Merchant Republic": {
                "tag": "MER", "government_type": "oligarchic_republic", "ruler_name": "Council of Merchants",
                "treasury": 1200.0, "prestige": 55.0, "legitimacy": 65.0
            },
            "Free Cities": {
                "tag": "FRE", "government_type": "city_state", "ruler_name": "Mayor Wilhelm",
                "treasury": 600.0, "prestige": 30.0, "legitimacy": 60.0
            }
        }

        for owner in unique_owners:
            template = country_templates.get(owner, {
                "tag": owner[:3].upper(), "government_type": "monarchy", "ruler_name": "Unknown Ruler",
                "treasury": 1000.0, "prestige": 50.0, "legitimacy": 75.0
            })

            # Find capital (first province owned)
            capital = next((p.name for p in self.provinces if p.owner == owner), "Unknown")

            # Get provinces owned by this country
            owned_provinces = [p.name for p in self.provinces if p.owner == owner]

            country = {
                "name": owner,
                "tag": template["tag"],
                "government_type": template["government_type"],
                "ruler_name": template["ruler_name"],
                "capital": capital,
                "provinces": owned_provinces,

                # Economic
                "treasury": template["treasury"],
                "monthly_income": 0.0,
                "monthly_expenses": 0.0,
                "inflation": 0.0,
                "trade_efficiency": 0.5,

                # Military
                "army_size": len(owned_provinces) * 1000,  # 1000 troops per province
                "navy_size": len([p for p in self.provinces if p.owner == owner and p.terrain == "coast"]) * 5,
                "military_tradition": 0.0,

                # Technology & Research
                "technologies": [],
                "research_points": {"military": 0, "economic": 0, "social": 0, "administrative": 0},
                "current_research": {},

                # Diplomacy
                "prestige": template["prestige"],
                "diplomatic_reputation": 0.0,
                "relations": {},

                # Social
                "stability": 50.0,
                "war_exhaustion": 0.0,
                "legitimacy": template["legitimacy"],

                # Modifiers
                "national_modifiers": {},

                # For compatibility with frontend
                "geometry": self._create_country_geometry(owned_provinces),
                "properties": {"NAME": owner}
            }

            countries.append(country)

        # Try to load additional countries from GeoJSON if file exists
        if geojson_path.exists():
            try:
                with open(geojson_path, "r", encoding="utf-8") as f:
                    data = json.load(f)

                # Add any additional countries from GeoJSON that aren't already created
                for feature in data.get("features", [])[:5]:  # Limit to first 5 for performance
                    properties = feature.get("properties", {})
                    name = properties.get("NAME") or properties.get("name") or "Unknown"

                    if name not in [c["name"] for c in countries]:
                        country = {
                            "name": name,
                            "tag": name[:3].upper(),
                            "government_type": "monarchy",
                            "ruler_name": "Unknown Ruler",
                            "capital": "Unknown",
                            "provinces": [],
                            "treasury": 500.0,
                            "monthly_income": 0.0,
                            "monthly_expenses": 0.0,
                            "inflation": 0.0,
                            "trade_efficiency": 0.3,
                            "army_size": 500,
                            "navy_size": 0,
                            "military_tradition": 0.0,
                            "technologies": [],
                            "research_points": {"military": 0, "economic": 0, "social": 0, "administrative": 0},
                            "current_research": {},
                            "prestige": 25.0,
                            "diplomatic_reputation": 0.0,
                            "relations": {},
                            "stability": 50.0,
                            "war_exhaustion": 0.0,
                            "legitimacy": 50.0,
                            "national_modifiers": {},
                            "geometry": feature.get("geometry"),
                            "properties": {"NAME": name}
                        }
                        countries.append(country)

            except Exception as e:
                logging.warning(f"Could not load additional countries from GeoJSON: {e}")

        logging.info(f"Created {len(countries)} countries with enhanced game data.")
        return countries

    def _create_country_geometry(self, province_names):
        """Create a simple geometry for countries based on their provinces"""
        # This is a placeholder - in a real game you'd combine province geometries
        return {
            "type": "Polygon",
            "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]]
        }

# Advanced Game Systems
class ResourceManager:
    def __init__(self):
        self.global_resources = {
            "grain": Resource(name="grain", base_value=2.0, current_price=2.0, demand=1.0, supply=1.0),
            "iron": Resource(name="iron", base_value=5.0, current_price=5.0, demand=1.0, supply=1.0),
            "coal": Resource(name="coal", base_value=3.0, current_price=3.0, demand=1.0, supply=1.0),
            "textiles": Resource(name="textiles", base_value=8.0, current_price=8.0, demand=1.0, supply=1.0),
            "luxury_goods": Resource(name="luxury_goods", base_value=15.0, current_price=15.0, demand=1.0, supply=1.0),
            "timber": Resource(name="timber", base_value=1.5, current_price=1.5, demand=1.0, supply=1.0),
            "fish": Resource(name="fish", base_value=1.8, current_price=1.8, demand=1.0, supply=1.0),
            "precious_metals": Resource(name="precious_metals", base_value=25.0, current_price=25.0, demand=1.0, supply=1.0)
        }

    def update_prices(self):
        """Update global resource prices based on supply and demand"""
        for resource in self.global_resources.values():
            # Price fluctuation based on supply/demand ratio
            ratio = resource.demand / max(resource.supply, 0.1)
            price_change = (ratio - 1.0) * 0.1  # 10% max change per turn

            # Add some volatility
            volatility_change = random.uniform(-resource.volatility, resource.volatility)

            resource.current_price = max(0.1, resource.current_price * (1 + price_change + volatility_change))

            # Reset supply/demand for next calculation
            resource.supply = 0.1  # Base supply
            resource.demand = 0.1  # Base demand

class PopSystem:
    def __init__(self):
        self.social_classes = ["aristocrat", "bourgeois", "artisan", "farmer", "laborer"]
        self.cultures = ["imperial", "highland", "coastal", "nomadic", "merchant"]
        self.religions = ["orthodox", "reformed", "traditional", "mystical"]

    def update(self, countries, provinces):
        """Update population dynamics including growth, migration, and social mobility"""
        for country in countries:
            country_provinces = [p for p in provinces if p.owner == country["name"]]

            for province in country_provinces:
                # Population growth
                self._calculate_population_growth(province, country)

                # Social mobility
                self._calculate_social_mobility(province)

                # Update happiness and militancy
                self._update_pop_attitudes(province, country)

    def _calculate_population_growth(self, province, country):
        """Calculate population growth based on various factors"""
        base_growth = 0.01  # 1% base growth

        # Factors affecting growth
        development_bonus = province.development / 100 * 0.005
        stability_bonus = (country["stability"] - 50) / 100 * 0.01
        unrest_penalty = province.unrest * 0.002

        growth_rate = base_growth + development_bonus + stability_bonus - unrest_penalty
        growth_rate = max(-0.02, min(0.03, growth_rate))  # Cap between -2% and 3%

        for pop_group in province.population_groups:
            pop_group.size = int(pop_group.size * (1 + growth_rate))

    def _calculate_social_mobility(self, province):
        """Handle social mobility between classes"""
        # Simplified social mobility - some pops can change class based on wealth
        for pop_group in province.population_groups:
            if pop_group.social_class == "farmer" and pop_group.wealth > 15:
                if random.random() < 0.02:  # 2% chance
                    pop_group.social_class = "artisan"
            elif pop_group.social_class == "artisan" and pop_group.wealth > 25:
                if random.random() < 0.01:  # 1% chance
                    pop_group.social_class = "bourgeois"

    def _update_pop_attitudes(self, province, country):
        """Update population happiness and militancy with sophisticated factors"""
        for pop_group in province.population_groups:
            # Base happiness from wealth
            wealth_happiness = min(pop_group.wealth / 20, 2.0)

            # Stability affects happiness
            stability_effect = (country["stability"] - 50) / 50

            # Government type affects different classes differently
            gov_effect = self._get_government_happiness_modifier(pop_group.social_class, country["government_type"])

            # Cultural and religious factors
            cultural_effect = self._get_cultural_happiness_modifier(pop_group, province, country)

            # Update happiness
            pop_group.happiness = max(0, min(10, wealth_happiness + stability_effect + gov_effect + cultural_effect))

            # Consciousness increases with education and events
            consciousness_change = 0.01  # Base increase
            if pop_group.social_class in ["bourgeois", "artisan"]:
                consciousness_change += 0.02  # More educated classes
            pop_group.consciousness = min(10, pop_group.consciousness + consciousness_change)

            # Militancy increases with low happiness and high consciousness
            if pop_group.happiness < 3:
                militancy_increase = 0.1 + (pop_group.consciousness / 50)
                pop_group.militancy = min(10, pop_group.militancy + militancy_increase)
            else:
                pop_group.militancy = max(0, pop_group.militancy - 0.05)

            # Handle migration and cultural conversion
            self._process_pop_migration(pop_group, province, country)
            self._process_cultural_conversion(pop_group, province)

    def _get_government_happiness_modifier(self, social_class, government_type):
        """Get happiness modifier based on government type and social class"""
        modifiers = {
            "absolute_monarchy": {
                "aristocrat": 1.0, "bourgeois": -0.5, "artisan": 0.0, "farmer": 0.2, "laborer": -0.2
            },
            "merchant_republic": {
                "aristocrat": -0.5, "bourgeois": 1.5, "artisan": 0.5, "farmer": -0.2, "laborer": -0.3
            },
            "feudal_monarchy": {
                "aristocrat": 0.8, "bourgeois": -0.2, "artisan": 0.2, "farmer": 0.5, "laborer": 0.0
            },
            "oligarchic_republic": {
                "aristocrat": 0.3, "bourgeois": 1.0, "artisan": 0.3, "farmer": -0.1, "laborer": -0.5
            },
            "city_state": {
                "aristocrat": 0.0, "bourgeois": 0.8, "artisan": 1.0, "farmer": 0.2, "laborer": 0.3
            }
        }
        return modifiers.get(government_type, {}).get(social_class, 0.0)

    def _get_cultural_happiness_modifier(self, pop_group, province, country):
        """Get happiness modifier based on cultural and religious factors"""
        modifier = 0.0

        # Cultural acceptance - dominant culture in province gets bonus
        province_cultures = [pop.culture for pop in province.population_groups]
        dominant_culture = max(set(province_cultures), key=province_cultures.count)

        if pop_group.culture == dominant_culture:
            modifier += 0.3
        else:
            modifier -= 0.2  # Cultural minority penalty

        # Religious tolerance
        province_religions = [pop.religion for pop in province.population_groups]
        dominant_religion = max(set(province_religions), key=province_religions.count)

        if pop_group.religion == dominant_religion:
            modifier += 0.2
        else:
            modifier -= 0.1  # Religious minority penalty

        return modifier

    def _process_pop_migration(self, pop_group, province, country):
        """Handle population migration based on conditions"""
        # Migration happens when happiness is very low or very high
        migration_chance = 0.0

        if pop_group.happiness < 2.0 and pop_group.militancy > 5.0:
            # Emigration due to poor conditions
            migration_chance = 0.02  # 2% chance to emigrate
        elif pop_group.happiness > 8.0 and province.development > 70:
            # Immigration attraction due to good conditions
            migration_chance = -0.01  # 1% chance of immigration (negative = growth)

        if random.random() < abs(migration_chance):
            if migration_chance > 0:
                # Emigration
                emigrants = int(pop_group.size * 0.05)  # 5% emigrate
                pop_group.size = max(100, pop_group.size - emigrants)
            else:
                # Immigration
                immigrants = int(pop_group.size * 0.02)  # 2% immigration
                pop_group.size += immigrants

    def _process_cultural_conversion(self, pop_group, province):
        """Handle cultural and religious conversion over time"""
        # Cultural assimilation happens slowly
        province_cultures = [pop.culture for pop in province.population_groups]
        dominant_culture = max(set(province_cultures), key=province_cultures.count)

        if pop_group.culture != dominant_culture and random.random() < 0.005:  # 0.5% chance
            pop_group.culture = dominant_culture

        # Religious conversion is rarer
        province_religions = [pop.religion for pop in province.population_groups]
        dominant_religion = max(set(province_religions), key=province_religions.count)

        if pop_group.religion != dominant_religion and random.random() < 0.002:  # 0.2% chance
            pop_group.religion = dominant_religion

class EconomySystem:
    def __init__(self, resource_manager):
        self.resource_manager = resource_manager
        self.building_types = {
            "farm": {"cost": 100, "produces": "grain", "efficiency": 1.0},
            "mine": {"cost": 200, "produces": "iron", "efficiency": 1.0},
            "textile_mill": {"cost": 300, "produces": "textiles", "efficiency": 1.0},
            "lumber_mill": {"cost": 150, "produces": "timber", "efficiency": 1.0},
            "fishing_port": {"cost": 120, "produces": "fish", "efficiency": 1.0}
        }

    def update(self, countries, provinces):
        """Update economic systems including production, trade, and income"""
        # Reset resource supply/demand
        for resource in self.resource_manager.global_resources.values():
            resource.supply = 0.1
            resource.demand = 0.1

        # Calculate production and consumption
        for country in countries:
            country_provinces = [p for p in provinces if p.owner == country["name"]]

            # Calculate production
            total_production = self._calculate_production(country_provinces)

            # Calculate consumption and demand
            total_consumption = self._calculate_consumption(country_provinces)

            # Calculate trade income
            trade_income = self._calculate_trade_income(country, country_provinces)

            # Calculate taxes
            tax_income = self._calculate_tax_income(country, country_provinces)

            # Update country finances
            country["monthly_income"] = tax_income + trade_income
            country["monthly_expenses"] = self._calculate_expenses(country)
            country["treasury"] += country["monthly_income"] - country["monthly_expenses"]

            # Update inflation based on money supply
            self._update_inflation(country)

        # Update global prices
        self.resource_manager.update_prices()

    def _calculate_production(self, provinces):
        """Calculate resource production from provinces"""
        production = {}

        for province in provinces:
            # Base resource production from terrain
            base_production = self._get_base_production(province)

            # Building production
            building_production = self._get_building_production(province)

            # Combine and apply efficiency
            for resource, amount in {**base_production, **building_production}.items():
                efficiency = province.development / 100 * province.tax_efficiency
                actual_production = amount * efficiency

                production[resource] = production.get(resource, 0) + actual_production

                # Add to global supply
                if resource in self.resource_manager.global_resources:
                    self.resource_manager.global_resources[resource].supply += actual_production

        return production

    def _get_base_production(self, province):
        """Get base resource production based on terrain and climate"""
        production = {}

        if province.terrain in ["plains", "hills"]:
            production["grain"] = 2.0
        if province.terrain in ["mountains", "hills"]:
            production["iron"] = 1.0
            production["precious_metals"] = 0.2
        if province.terrain == "coast":
            production["fish"] = 1.5
        if province.climate in ["temperate", "tropical"]:
            production["timber"] = 1.0

        return production

    def _get_building_production(self, province):
        """Calculate production from buildings"""
        production = {}

        for building_type, count in province.buildings.items():
            if building_type in self.building_types:
                building_info = self.building_types[building_type]
                resource = building_info["produces"]
                amount = building_info["efficiency"] * count
                production[resource] = production.get(resource, 0) + amount

        return production

    def _calculate_consumption(self, provinces):
        """Calculate resource consumption by population"""
        consumption = {}

        for province in provinces:
            for pop_group in province.population_groups:
                # Basic consumption based on population size and wealth
                base_consumption = pop_group.size / 1000  # Scale factor
                wealth_multiplier = 1 + (pop_group.wealth / 20)

                # Different classes consume different amounts
                class_multipliers = {
                    "aristocrat": 3.0,
                    "bourgeois": 2.0,
                    "artisan": 1.5,
                    "farmer": 1.0,
                    "laborer": 0.8
                }

                multiplier = class_multipliers.get(pop_group.social_class, 1.0) * wealth_multiplier

                # Consume basic goods
                grain_consumption = base_consumption * multiplier
                consumption["grain"] = consumption.get("grain", 0) + grain_consumption

                # Add to global demand
                if "grain" in self.resource_manager.global_resources:
                    self.resource_manager.global_resources["grain"].demand += grain_consumption

                # Luxury consumption for wealthy classes
                if pop_group.social_class in ["aristocrat", "bourgeois"]:
                    luxury_consumption = base_consumption * 0.5
                    consumption["luxury_goods"] = consumption.get("luxury_goods", 0) + luxury_consumption
                    if "luxury_goods" in self.resource_manager.global_resources:
                        self.resource_manager.global_resources["luxury_goods"].demand += luxury_consumption

        return consumption

    def _calculate_trade_income(self, country, provinces):
        """Calculate income from trade"""
        trade_income = 0.0

        for province in provinces:
            for trade_route in province.trade_routes:
                if trade_route.from_province == province.name:
                    # Export income
                    resource_price = self.resource_manager.global_resources.get(trade_route.resource,
                                   Resource(name="unknown", base_value=1.0, current_price=1.0, demand=1.0, supply=1.0)).current_price
                    route_income = trade_route.volume * resource_price * trade_route.efficiency * country.get("trade_efficiency", 0.5)
                    trade_income += route_income

        return trade_income

    def _calculate_tax_income(self, country, provinces):
        """Calculate tax income from population"""
        tax_income = 0.0

        for province in provinces:
            province_tax = 0.0

            for pop_group in province.population_groups:
                # Tax based on population size and wealth
                pop_tax = pop_group.size * pop_group.wealth * 0.01  # 1% wealth tax

                # Different classes pay different rates
                class_tax_rates = {
                    "aristocrat": 0.05,  # 5% rate but often have exemptions
                    "bourgeois": 0.15,   # 15% rate
                    "artisan": 0.12,     # 12% rate
                    "farmer": 0.08,      # 8% rate
                    "laborer": 0.05      # 5% rate
                }

                tax_rate = class_tax_rates.get(pop_group.social_class, 0.1)
                pop_tax *= tax_rate

                # Apply province tax efficiency
                pop_tax *= province.tax_efficiency

                province_tax += pop_tax

            tax_income += province_tax

        return tax_income

    def _calculate_expenses(self, country):
        """Calculate country's monthly expenses"""
        expenses = 0.0

        # Military maintenance
        expenses += country.get("army_size", 0) * 2.0  # 2 gold per unit
        expenses += country.get("navy_size", 0) * 5.0  # 5 gold per ship

        # Administrative costs
        province_count = len(country.get("provinces", []))
        expenses += province_count * 1.0  # 1 gold per province

        # Interest on debt (if treasury is negative)
        if country.get("treasury", 0) < 0:
            expenses += abs(country["treasury"]) * 0.05  # 5% interest

        return expenses

    def _update_inflation(self, country):
        """Update country's inflation based on economic factors"""
        # Inflation increases with deficit spending
        monthly_balance = country["monthly_income"] - country["monthly_expenses"]

        if monthly_balance < 0:
            # Deficit increases inflation
            inflation_increase = abs(monthly_balance) / max(country["treasury"], 100) * 0.01
            country["inflation"] = min(1.0, country.get("inflation", 0) + inflation_increase)
        else:
            # Surplus decreases inflation slowly
            country["inflation"] = max(0.0, country.get("inflation", 0) - 0.001)

# Enhanced Event System with Meaningful Choices and Consequences
class GameEvent(BaseModel):
    id: str
    title: str
    description: str
    event_type: str  # economic, military, diplomatic, social, natural, technological
    trigger_conditions: Dict[str, Any] = {}
    choices: List[Dict[str, Any]] = []
    consequences: Dict[str, Any] = {}
    rarity: str = "common"  # common, uncommon, rare, legendary
    repeatable: bool = True
    cooldown_turns: int = 0
    historical: bool = False

class EventChoice(BaseModel):
    id: str
    text: str
    description: str
    requirements: Dict[str, Any] = {}
    immediate_effects: Dict[str, Any] = {}
    delayed_effects: Dict[str, Any] = {}
    probability_modifiers: Dict[str, float] = {}

class EventSystem:
    def __init__(self):
        self.active_events = {}  # country_name -> list of active events
        self.event_history = {}  # country_name -> list of past events
        self.event_cooldowns = {}  # event_id -> turns remaining
        self.event_templates = self._initialize_event_templates()
        self.turn_counter = 0

    def _initialize_event_templates(self):
        """Initialize comprehensive event templates with meaningful choices"""
        events = {}

        # Economic Events
        events["trade_dispute"] = GameEvent(
            id="trade_dispute",
            title="Trade Route Disruption",
            description="Bandits have been attacking merchant caravans along our major trade routes. Our merchants are demanding action, but military intervention would be costly.",
            event_type="economic",
            trigger_conditions={"trade_efficiency": {"min": 0.3}, "treasury": {"min": 100}},
            choices=[
                {
                    "id": "military_escort",
                    "text": "Deploy Military Escorts",
                    "description": "Send troops to protect trade routes. Expensive but effective.",
                    "requirements": {"army_size": {"min": 1000}},
                    "immediate_effects": {
                        "treasury": -200,
                        "trade_efficiency": 0.15,
                        "military_expenses": 50
                    },
                    "delayed_effects": {
                        "turns": 3,
                        "trade_efficiency": 0.1,
                        "prestige": 5
                    }
                },
                {
                    "id": "negotiate_bandits",
                    "text": "Negotiate with Bandit Leaders",
                    "description": "Attempt diplomatic solution. Risky but cheaper.",
                    "requirements": {"diplomatic_reputation": {"min": 10}},
                    "immediate_effects": {
                        "treasury": -50,
                        "stability": -5
                    },
                    "probability_modifiers": {
                        "success": 0.6,
                        "failure": 0.4
                    }
                },
                {
                    "id": "ignore_problem",
                    "text": "Ignore the Problem",
                    "description": "Let merchants handle it themselves. No cost but consequences.",
                    "immediate_effects": {
                        "trade_efficiency": -0.2,
                        "stability": -10
                    },
                    "delayed_effects": {
                        "turns": 5,
                        "trade_efficiency": -0.1,
                        "prestige": -3
                    }
                }
            ],
            rarity="common",
            repeatable=True,
            cooldown_turns=10
        )

        events["economic_boom"] = GameEvent(
            id="economic_boom",
            title="Economic Prosperity",
            description="New trade opportunities have emerged! Foreign merchants are eager to establish trade agreements. How should we capitalize on this opportunity?",
            event_type="economic",
            trigger_conditions={"stability": {"min": 60}, "trade_efficiency": {"min": 0.4}},
            choices=[
                {
                    "id": "expand_trade",
                    "text": "Aggressively Expand Trade",
                    "description": "Invest heavily in new trade infrastructure and agreements.",
                    "requirements": {"treasury": {"min": 300}},
                    "immediate_effects": {
                        "treasury": -300,
                        "trade_efficiency": 0.25
                    },
                    "delayed_effects": {
                        "turns": 4,
                        "monthly_income": 100,
                        "prestige": 10
                    }
                },
                {
                    "id": "cautious_expansion",
                    "text": "Cautious Expansion",
                    "description": "Moderate investment with lower risk.",
                    "requirements": {"treasury": {"min": 150}},
                    "immediate_effects": {
                        "treasury": -150,
                        "trade_efficiency": 0.15
                    },
                    "delayed_effects": {
                        "turns": 3,
                        "monthly_income": 50
                    }
                },
                {
                    "id": "maintain_status",
                    "text": "Maintain Current Trade",
                    "description": "Keep current trade levels without expansion.",
                    "immediate_effects": {
                        "stability": 5
                    }
                }
            ],
            rarity="uncommon",
            repeatable=True,
            cooldown_turns=15
        )

        # Military Events
        events["border_tension"] = GameEvent(
            id="border_tension",
            title="Border Tensions Rise",
            description="Tensions are escalating with a neighboring country over disputed territory. Military advisors recommend different approaches to handle the situation.",
            event_type="military",
            trigger_conditions={"army_size": {"min": 500}, "prestige": {"min": 20}},
            choices=[
                {
                    "id": "military_buildup",
                    "text": "Military Buildup",
                    "description": "Show strength by mobilizing forces near the border.",
                    "requirements": {"treasury": {"min": 200}},
                    "immediate_effects": {
                        "treasury": -200,
                        "military_tradition": 5,
                        "war_exhaustion": 10
                    },
                    "probability_modifiers": {
                        "neighbor_backs_down": 0.7,
                        "escalation": 0.3
                    }
                },
                {
                    "id": "diplomatic_solution",
                    "text": "Seek Diplomatic Resolution",
                    "description": "Attempt to resolve through negotiation.",
                    "requirements": {"diplomatic_reputation": {"min": 15}},
                    "immediate_effects": {
                        "diplomatic_reputation": 5,
                        "prestige": -2
                    },
                    "probability_modifiers": {
                        "peaceful_resolution": 0.8,
                        "seen_as_weak": 0.2
                    }
                },
                {
                    "id": "preemptive_strike",
                    "text": "Preemptive Strike",
                    "description": "Launch a surprise attack to secure the territory.",
                    "requirements": {"army_size": {"min": 2000}, "military_tradition": {"min": 10}},
                    "immediate_effects": {
                        "war_exhaustion": 20,
                        "stability": -15,
                        "prestige": 10
                    },
                    "probability_modifiers": {
                        "quick_victory": 0.6,
                        "prolonged_conflict": 0.4
                    }
                }
            ],
            rarity="uncommon",
            repeatable=True,
            cooldown_turns=20
        )

        # Social Events
        events["social_unrest"] = GameEvent(
            id="social_unrest",
            title="Social Unrest Brewing",
            description="Different social classes are becoming increasingly dissatisfied with current policies. Protests are beginning to form in major cities.",
            event_type="social",
            trigger_conditions={"stability": {"max": 40}},
            choices=[
                {
                    "id": "address_grievances",
                    "text": "Address Grievances",
                    "description": "Implement reforms to address the concerns of different social groups.",
                    "requirements": {"treasury": {"min": 250}},
                    "immediate_effects": {
                        "treasury": -250,
                        "stability": 20,
                        "legitimacy": 10
                    },
                    "delayed_effects": {
                        "turns": 2,
                        "pop_happiness_bonus": 1.0
                    }
                },
                {
                    "id": "suppress_unrest",
                    "text": "Suppress with Force",
                    "description": "Use military force to quell the protests.",
                    "requirements": {"army_size": {"min": 1500}},
                    "immediate_effects": {
                        "stability": 10,
                        "legitimacy": -15,
                        "war_exhaustion": 5
                    },
                    "delayed_effects": {
                        "turns": 3,
                        "pop_militancy_increase": 1.0
                    }
                },
                {
                    "id": "ignore_protests",
                    "text": "Ignore the Protests",
                    "description": "Hope the unrest dies down naturally.",
                    "immediate_effects": {
                        "stability": -10
                    },
                    "probability_modifiers": {
                        "escalation": 0.7,
                        "natural_resolution": 0.3
                    }
                }
            ],
            rarity="common",
            repeatable=True,
            cooldown_turns=8
        )

        # Natural Disaster Events
        events["natural_disaster"] = GameEvent(
            id="natural_disaster",
            title="Natural Disaster Strikes",
            description="A devastating earthquake has struck one of our provinces, causing significant damage to infrastructure and population.",
            event_type="natural",
            trigger_conditions={},  # Can happen to anyone
            choices=[
                {
                    "id": "emergency_relief",
                    "text": "Emergency Relief Effort",
                    "description": "Mobilize all available resources for immediate disaster relief.",
                    "requirements": {"treasury": {"min": 400}},
                    "immediate_effects": {
                        "treasury": -400,
                        "stability": 15,
                        "legitimacy": 10
                    },
                    "delayed_effects": {
                        "turns": 2,
                        "province_development_recovery": 0.8
                    }
                },
                {
                    "id": "limited_aid",
                    "text": "Limited Aid",
                    "description": "Provide basic assistance within budget constraints.",
                    "requirements": {"treasury": {"min": 150}},
                    "immediate_effects": {
                        "treasury": -150,
                        "stability": 5
                    },
                    "delayed_effects": {
                        "turns": 4,
                        "province_development_recovery": 0.5
                    }
                },
                {
                    "id": "minimal_response",
                    "text": "Minimal Response",
                    "description": "Let the province recover on its own.",
                    "immediate_effects": {
                        "stability": -20,
                        "legitimacy": -15
                    },
                    "delayed_effects": {
                        "turns": 8,
                        "province_development_recovery": 0.2
                    }
                }
            ],
            rarity="rare",
            repeatable=True,
            cooldown_turns=25
        )

        return events

    def update(self, countries, provinces):
        """Update event system each turn - trigger new events and process ongoing ones"""
        self.turn_counter += 1

        # Decrease cooldowns
        for event_id in list(self.event_cooldowns.keys()):
            self.event_cooldowns[event_id] -= 1
            if self.event_cooldowns[event_id] <= 0:
                del self.event_cooldowns[event_id]

        # Process events for each country
        for country in countries:
            country_name = country["name"]

            # Initialize country event tracking if needed
            if country_name not in self.active_events:
                self.active_events[country_name] = []
            if country_name not in self.event_history:
                self.event_history[country_name] = []

            # Check for new events to trigger
            self._check_event_triggers(country, provinces)

            # Process delayed effects from previous events
            self._process_delayed_effects(country)

    def _check_event_triggers(self, country, provinces):
        """Check if any events should trigger for this country"""
        country_name = country["name"]

        # Don't trigger new events if country already has active events
        if len(self.active_events[country_name]) >= 2:
            return

        # Calculate trigger probabilities based on country state
        for event_id, event_template in self.event_templates.items():
            # Skip if event is on cooldown
            if event_id in self.event_cooldowns:
                continue

            # Check if event meets trigger conditions
            if not self._meets_trigger_conditions(country, event_template.trigger_conditions):
                continue

            # Calculate probability based on rarity and country state
            base_probability = self._get_base_probability(event_template.rarity)
            modified_probability = self._modify_probability(country, event_template, base_probability)

            # Roll for event trigger
            if random.random() < modified_probability:
                self._trigger_event(country_name, event_template)
                break  # Only one event per turn per country

    def _meets_trigger_conditions(self, country, conditions):
        """Check if country meets the trigger conditions for an event"""
        for stat, requirements in conditions.items():
            country_value = country.get(stat, 0)

            if "min" in requirements and country_value < requirements["min"]:
                return False
            if "max" in requirements and country_value > requirements["max"]:
                return False

        return True

    def _get_base_probability(self, rarity):
        """Get base probability for event based on rarity"""
        probabilities = {
            "common": 0.15,      # 15% chance per turn
            "uncommon": 0.08,    # 8% chance per turn
            "rare": 0.03,        # 3% chance per turn
            "legendary": 0.01    # 1% chance per turn
        }
        return probabilities.get(rarity, 0.05)

    def _modify_probability(self, country, event_template, base_probability):
        """Modify event probability based on country state and event type"""
        modifier = 1.0

        # Modify based on event type and country state
        if event_template.event_type == "economic":
            if country.get("stability", 50) < 30:
                modifier *= 1.5  # More economic events when unstable
            if country.get("treasury", 0) < 100:
                modifier *= 1.3  # More economic events when poor

        elif event_template.event_type == "military":
            if country.get("war_exhaustion", 0) > 50:
                modifier *= 1.4  # More military events during war
            if country.get("army_size", 0) > 5000:
                modifier *= 1.2  # More military events with large army

        elif event_template.event_type == "social":
            if country.get("stability", 50) < 40:
                modifier *= 2.0  # Much more social unrest when unstable
            if country.get("legitimacy", 50) < 30:
                modifier *= 1.5  # More social events with low legitimacy

        elif event_template.event_type == "natural":
            # Natural disasters are random but slightly more likely in certain conditions
            modifier *= 1.0

        return min(base_probability * modifier, 0.4)  # Cap at 40% chance

    def _trigger_event(self, country_name, event_template):
        """Trigger a new event for the country"""
        # Create event instance with unique ID
        event_instance = {
            "id": f"{event_template.id}_{self.turn_counter}_{country_name}",
            "template_id": event_template.id,
            "title": event_template.title,
            "description": event_template.description,
            "event_type": event_template.event_type,
            "choices": event_template.choices,
            "triggered_turn": self.turn_counter,
            "status": "active"
        }

        # Add to active events
        self.active_events[country_name].append(event_instance)

        # Set cooldown
        if event_template.cooldown_turns > 0:
            self.event_cooldowns[event_template.id] = event_template.cooldown_turns

        logging.info(f"Event '{event_template.title}' triggered for {country_name}")

    def _process_delayed_effects(self, country):
        """Process any delayed effects from previous event choices"""
        country_name = country["name"]

        # Check event history for delayed effects that should trigger this turn
        for event_record in self.event_history[country_name]:
            if "delayed_effects" in event_record and "trigger_turn" in event_record["delayed_effects"]:
                if self.turn_counter >= event_record["delayed_effects"]["trigger_turn"]:
                    self._apply_delayed_effects(country, event_record["delayed_effects"])
                    # Remove the delayed effect so it doesn't trigger again
                    del event_record["delayed_effects"]

    def _apply_delayed_effects(self, country, delayed_effects):
        """Apply delayed effects from a previous event choice"""
        for effect_type, value in delayed_effects.items():
            if effect_type in ["trigger_turn"]:
                continue  # Skip metadata

            if effect_type in country:
                if isinstance(value, (int, float)):
                    country[effect_type] = country.get(effect_type, 0) + value
                    # Clamp values to reasonable ranges
                    if effect_type == "stability":
                        country[effect_type] = max(0, min(100, country[effect_type]))
                    elif effect_type == "legitimacy":
                        country[effect_type] = max(0, min(100, country[effect_type]))
                    elif effect_type == "prestige":
                        country[effect_type] = max(0, min(100, country[effect_type]))

        logging.info(f"Applied delayed effects to {country['name']}: {delayed_effects}")

    def get_active_events(self, country_name):
        """Get all active events for a country"""
        return self.active_events.get(country_name, [])

    def get_random_event(self, country_name=None):
        """Get a random active event for the country (for API compatibility)"""
        if country_name and country_name in self.active_events:
            active_events = self.active_events[country_name]
            if active_events:
                return active_events[0]  # Return the first active event

        # Fallback to old system if no active events
        return None

    def apply_event_choice(self, country_name, event_id, choice_id, countries):
        """Apply the effects of a player's event choice"""
        # Find the active event
        active_events = self.active_events.get(country_name, [])
        event_instance = None

        for event in active_events:
            if event["id"] == event_id:
                event_instance = event
                break

        if not event_instance:
            return {"success": False, "error": "Event not found"}

        # Find the chosen choice
        chosen_choice = None
        for choice in event_instance["choices"]:
            if choice["id"] == choice_id:
                chosen_choice = choice
                break

        if not chosen_choice:
            return {"success": False, "error": "Choice not found"}

        # Get the country to apply effects to
        country = None
        for c in countries:
            if c["name"] == country_name:
                country = c
                break

        if not country:
            return {"success": False, "error": "Country not found"}

        # Check requirements
        requirements_met = self._check_choice_requirements(country, chosen_choice.get("requirements", {}))
        if not requirements_met:
            return {"success": False, "error": "Requirements not met for this choice"}

        # Apply immediate effects
        self._apply_immediate_effects(country, chosen_choice.get("immediate_effects", {}))

        # Handle probability-based outcomes
        outcome = self._resolve_probability_outcomes(chosen_choice.get("probability_modifiers", {}))

        # Schedule delayed effects if any
        delayed_effects = chosen_choice.get("delayed_effects", {})
        if delayed_effects and "turns" in delayed_effects:
            delayed_effects["trigger_turn"] = self.turn_counter + delayed_effects["turns"]

        # Create event record for history
        event_record = {
            "event_id": event_id,
            "template_id": event_instance["template_id"],
            "title": event_instance["title"],
            "choice_made": choice_id,
            "choice_text": chosen_choice["text"],
            "turn_resolved": self.turn_counter,
            "outcome": outcome,
            "delayed_effects": delayed_effects if delayed_effects else None
        }

        # Add to history
        if country_name not in self.event_history:
            self.event_history[country_name] = []
        self.event_history[country_name].append(event_record)

        # Remove from active events
        self.active_events[country_name] = [e for e in self.active_events[country_name] if e["id"] != event_id]

        logging.info(f"Event choice applied: {country_name} chose '{chosen_choice['text']}' for event '{event_instance['title']}'")

        return {
            "success": True,
            "outcome": outcome,
            "message": f"Applied choice: {chosen_choice['text']}",
            "effects_applied": chosen_choice.get("immediate_effects", {}),
            "delayed_effects": delayed_effects
        }

    def _check_choice_requirements(self, country, requirements):
        """Check if country meets requirements for a choice"""
        for stat, requirement in requirements.items():
            country_value = country.get(stat, 0)

            if "min" in requirement and country_value < requirement["min"]:
                return False
            if "max" in requirement and country_value > requirement["max"]:
                return False

        return True

    def _apply_immediate_effects(self, country, effects):
        """Apply immediate effects from an event choice"""
        for effect_type, value in effects.items():
            if effect_type in country:
                if isinstance(value, (int, float)):
                    country[effect_type] = country.get(effect_type, 0) + value

                    # Clamp values to reasonable ranges
                    if effect_type == "stability":
                        country[effect_type] = max(0, min(100, country[effect_type]))
                    elif effect_type == "legitimacy":
                        country[effect_type] = max(0, min(100, country[effect_type]))
                    elif effect_type == "prestige":
                        country[effect_type] = max(0, min(100, country[effect_type]))
                    elif effect_type == "treasury":
                        country[effect_type] = max(-10000, country[effect_type])  # Allow debt but limit it

        logging.info(f"Applied immediate effects to {country['name']}: {effects}")

    def _resolve_probability_outcomes(self, probability_modifiers):
        """Resolve probability-based outcomes from event choices"""
        if not probability_modifiers:
            return "standard"

        # Roll for each possible outcome
        roll = random.random()
        cumulative_probability = 0.0

        for outcome, probability in probability_modifiers.items():
            cumulative_probability += probability
            if roll <= cumulative_probability:
                return outcome

        return "standard"  # Default outcome

    def apply_event(self, countries, country_name, effect):
        """Legacy method for backward compatibility"""
        # This is kept for API compatibility but should be replaced with apply_event_choice
        for c in countries:
            if c["name"] == country_name:
                if "+10 stability" in effect:
                    c["stability"] = c.get("stability", 100) + 10
                if "-5 stability" in effect:
                    c["stability"] = c.get("stability", 100) - 5
                if "+20 resources" in effect:
                    c["treasury"] = c.get("treasury", 0) + 20  # Convert resources to treasury
                # Clamp stability
                c["stability"] = max(0, min(100, c.get("stability", 100)))
                return c
        return None

# Military System Classes
class MilitaryUnit(BaseModel):
    name: str
    unit_type: str  # infantry, cavalry, artillery, frigate, ship_of_line
    size: int
    strength: float  # 0-100
    experience: float  # 0-100
    morale: float  # 0-100
    location: str  # province name
    maintenance_cost: float

class MilitarySystem:
    def __init__(self):
        self.unit_types = {
            "infantry": {"cost": 100, "maintenance": 1.0, "strength": 10, "speed": 1},
            "cavalry": {"cost": 200, "maintenance": 2.0, "strength": 15, "speed": 2},
            "artillery": {"cost": 300, "maintenance": 3.0, "strength": 20, "speed": 0.5},
            "frigate": {"cost": 500, "maintenance": 5.0, "strength": 25, "speed": 3},
            "ship_of_line": {"cost": 1000, "maintenance": 10.0, "strength": 50, "speed": 2}
        }

        self.terrain_modifiers = {
            "plains": {"infantry": 1.0, "cavalry": 1.2, "artillery": 1.1},
            "hills": {"infantry": 1.1, "cavalry": 0.8, "artillery": 0.9},
            "mountains": {"infantry": 1.2, "cavalry": 0.6, "artillery": 0.7},
            "coast": {"infantry": 1.0, "cavalry": 1.0, "artillery": 1.0},
            "forest": {"infantry": 1.1, "cavalry": 0.7, "artillery": 0.8}
        }

    def update(self, countries, provinces):
        """Update military systems including unit maintenance, experience, and morale"""
        for country in countries:
            # Initialize military units if not present
            if "military_units" not in country:
                country["military_units"] = []
                # Create some initial units for established countries
                if len(country.get("provinces", [])) > 0:
                    self._create_initial_units(country)

            # Update military units
            self._update_military_units(country)

            # Calculate military maintenance costs
            self._calculate_military_costs(country)

            # Update military tradition
            self._update_military_tradition(country)

    def _create_initial_units(self, country):
        """Create initial military units for a country"""
        capital = country.get("provinces", ["Unknown"])[0]

        # Create basic army based on country size
        num_provinces = len(country.get("provinces", []))

        # Infantry units (1 per province)
        for i in range(num_provinces):
            unit = {
                "name": f"{country['name']} Infantry #{i+1}",
                "unit_type": "infantry",
                "size": 1000,
                "strength": 100.0,
                "experience": random.uniform(10, 30),
                "morale": 80.0,
                "location": capital,
                "maintenance_cost": 1.0
            }
            country["military_units"].append(unit)

        # Add some cavalry and artillery for larger countries
        if num_provinces >= 3:
            cavalry_unit = {
                "name": f"{country['name']} Cavalry",
                "unit_type": "cavalry",
                "size": 500,
                "strength": 100.0,
                "experience": random.uniform(15, 35),
                "morale": 85.0,
                "location": capital,
                "maintenance_cost": 1.0
            }
            country["military_units"].append(cavalry_unit)

        if num_provinces >= 5:
            artillery_unit = {
                "name": f"{country['name']} Artillery",
                "unit_type": "artillery",
                "size": 200,
                "strength": 100.0,
                "experience": random.uniform(5, 25),
                "morale": 75.0,
                "location": capital,
                "maintenance_cost": 0.6
            }
            country["military_units"].append(artillery_unit)

    def _update_military_units(self, country):
        """Update military unit status"""
        for unit in country["military_units"]:
            # Experience gain over time
            unit["experience"] = min(100, unit["experience"] + 0.1)

            # Morale affected by supply and pay
            if country["treasury"] > 0:
                unit["morale"] = min(100, unit["morale"] + 0.5)
            else:
                unit["morale"] = max(0, unit["morale"] - 2.0)

            # Strength recovery when not in combat
            if unit["strength"] < 100:
                unit["strength"] = min(100, unit["strength"] + 1.0)

    def _calculate_military_costs(self, country):
        """Calculate military maintenance costs"""
        total_maintenance = 0.0

        for unit in country["military_units"]:
            unit_type_data = self.unit_types.get(unit["unit_type"], {"maintenance": 1.0})
            total_maintenance += unit_type_data["maintenance"] * unit["size"] / 1000

        # Update country expenses
        country["military_expenses"] = total_maintenance

    def _update_military_tradition(self, country):
        """Update military tradition based on various factors"""
        tradition_gain = 0.0

        # Gain from having experienced units
        if country["military_units"]:
            avg_experience = sum(unit["experience"] for unit in country["military_units"]) / len(country["military_units"])
            tradition_gain += avg_experience / 1000  # Small gain from experience

        # Gain from military technologies
        military_techs = [tech for tech in country.get("technologies", []) if "military" in tech.lower()]
        tradition_gain += len(military_techs) * 0.1

        country["military_tradition"] = min(100, country.get("military_tradition", 0) + tradition_gain)

    def create_unit(self, country, unit_type, province_name, size=1000):
        """Create a new military unit"""
        if unit_type not in self.unit_types:
            return False

        unit_data = self.unit_types[unit_type]
        cost = unit_data["cost"] * (size / 1000)

        if country["treasury"] < cost:
            return False

        country["treasury"] -= cost

        new_unit = {
            "name": f"{country['name']} {unit_type.title()} #{len(country['military_units']) + 1}",
            "unit_type": unit_type,
            "size": size,
            "strength": 100.0,
            "experience": 0.0,
            "morale": 80.0,
            "location": province_name,
            "maintenance_cost": unit_data["maintenance"] * (size / 1000)
        }

        country["military_units"].append(new_unit)
        return True

    def declare_war(self, aggressor_country, target_country, casus_belli="territorial_expansion"):
        """Declare war between two countries"""
        # Initialize war state if not present
        if "wars" not in aggressor_country:
            aggressor_country["wars"] = []
        if "wars" not in target_country:
            target_country["wars"] = []

        # Create war declaration
        war_id = f"war_{aggressor_country['name']}_{target_country['name']}_{len(aggressor_country['wars']) + 1}"

        war_data = {
            "id": war_id,
            "aggressor": aggressor_country["name"],
            "defender": target_country["name"],
            "casus_belli": casus_belli,
            "start_turn": 1,  # This should be current turn
            "war_score": 0,  # -100 to 100, positive favors aggressor
            "battles": [],
            "status": "active"
        }

        # Add war to both countries
        aggressor_country["wars"].append(war_data)
        target_country["wars"].append(war_data)

        # Increase war exhaustion
        aggressor_country["war_exhaustion"] = min(100, aggressor_country.get("war_exhaustion", 0) + 10)
        target_country["war_exhaustion"] = min(100, target_country.get("war_exhaustion", 0) + 5)

        # Decrease stability
        aggressor_country["stability"] = max(0, aggressor_country.get("stability", 50) - 5)
        target_country["stability"] = max(0, target_country.get("stability", 50) - 10)

        logging.info(f"War declared: {aggressor_country['name']} vs {target_country['name']}")
        return war_data

    def conduct_battle(self, attacker_country, defender_country, attacking_province, defending_province):
        """Conduct a battle between two countries"""
        # Get military units in the provinces
        attacker_units = [unit for unit in attacker_country.get("military_units", [])
                         if unit["location"] == attacking_province]
        defender_units = [unit for unit in defender_country.get("military_units", [])
                         if unit["location"] == defending_province]

        if not attacker_units:
            return {"success": False, "error": "No attacking units in province"}
        if not defender_units:
            return {"success": False, "error": "No defending units in province"}

        # Calculate battle strength
        attacker_strength = self._calculate_battle_strength(attacker_units, attacker_country, "attack")
        defender_strength = self._calculate_battle_strength(defender_units, defender_country, "defense")

        # Determine battle outcome
        total_strength = attacker_strength + defender_strength
        attacker_chance = attacker_strength / total_strength if total_strength > 0 else 0.5

        # Add some randomness
        battle_roll = random.random()
        attacker_wins = battle_roll < attacker_chance

        # Calculate casualties
        attacker_casualties = self._calculate_casualties(attacker_units, not attacker_wins)
        defender_casualties = self._calculate_casualties(defender_units, attacker_wins)

        # Apply casualties
        self._apply_casualties(attacker_units, attacker_casualties)
        self._apply_casualties(defender_units, defender_casualties)

        # Create battle result
        battle_result = {
            "attacker": attacker_country["name"],
            "defender": defender_country["name"],
            "attacking_province": attacking_province,
            "defending_province": defending_province,
            "attacker_strength": attacker_strength,
            "defender_strength": defender_strength,
            "winner": attacker_country["name"] if attacker_wins else defender_country["name"],
            "attacker_casualties": attacker_casualties,
            "defender_casualties": defender_casualties,
            "war_score_change": 5 if attacker_wins else -3
        }

        # Update war exhaustion
        attacker_country["war_exhaustion"] = min(100, attacker_country.get("war_exhaustion", 0) + 2)
        defender_country["war_exhaustion"] = min(100, defender_country.get("war_exhaustion", 0) + 2)

        # If attacker wins, they can potentially occupy the province
        if attacker_wins and defending_province != defender_country.get("capital"):
            # Check if province can be occupied
            remaining_defender_strength = sum(unit["size"] * (unit["strength"] / 100)
                                            for unit in defender_units if unit["size"] > 0)
            if remaining_defender_strength < 500:  # Threshold for occupation
                battle_result["province_occupied"] = True
                battle_result["occupation_details"] = {
                    "province": defending_province,
                    "occupier": attacker_country["name"],
                    "original_owner": defender_country["name"]
                }

        logging.info(f"Battle result: {battle_result}")
        return battle_result

    def _calculate_battle_strength(self, units, country, battle_type):
        """Calculate total battle strength for a set of units"""
        total_strength = 0

        for unit in units:
            # Base unit strength
            unit_type_data = self.unit_types.get(unit["unit_type"], {"strength": 10})
            base_strength = unit_type_data["strength"]

            # Apply unit condition modifiers
            strength_modifier = unit["strength"] / 100  # 0-1 based on unit condition
            morale_modifier = unit["morale"] / 100      # 0-1 based on morale
            experience_modifier = 1 + (unit["experience"] / 200)  # 1-1.5 based on experience

            # Apply country modifiers
            military_tradition_bonus = 1 + (country.get("military_tradition", 0) / 200)

            # Defensive bonus
            defensive_bonus = 1.2 if battle_type == "defense" else 1.0

            # Calculate final unit strength
            unit_strength = (base_strength * unit["size"] / 1000 *
                           strength_modifier * morale_modifier * experience_modifier *
                           military_tradition_bonus * defensive_bonus)

            total_strength += unit_strength

        return total_strength

    def _calculate_casualties(self, units, is_losing_side):
        """Calculate casualties for units in battle"""
        base_casualty_rate = 0.15 if is_losing_side else 0.08  # 15% for losers, 8% for winners

        total_casualties = 0
        for unit in units:
            # Add some randomness to casualties
            casualty_modifier = random.uniform(0.5, 1.5)
            unit_casualties = int(unit["size"] * base_casualty_rate * casualty_modifier)
            total_casualties += unit_casualties

        return total_casualties

    def _apply_casualties(self, units, total_casualties):
        """Apply casualties to units"""
        if total_casualties <= 0:
            return

        # Distribute casualties proportionally among units
        total_size = sum(unit["size"] for unit in units)
        if total_size <= 0:
            return

        for unit in units:
            unit_proportion = unit["size"] / total_size
            unit_casualties = int(total_casualties * unit_proportion)

            # Apply casualties
            unit["size"] = max(0, unit["size"] - unit_casualties)

            # Reduce strength and morale for surviving units
            if unit["size"] > 0:
                unit["strength"] = max(20, unit["strength"] - random.uniform(5, 15))
                unit["morale"] = max(10, unit["morale"] - random.uniform(10, 25))

    def siege_province(self, attacker_country, target_province, provinces):
        """Attempt to siege and capture a province"""
        # Find the province
        province = None
        for p in provinces:
            if p.name == target_province:
                province = p
                break

        if not province:
            return {"success": False, "error": "Province not found"}

        # Check if attacker has units in adjacent provinces or the province itself
        attacker_units = [unit for unit in attacker_country.get("military_units", [])
                         if unit["location"] == target_province or
                         self._is_adjacent_province(unit["location"], target_province)]

        if not attacker_units:
            return {"success": False, "error": "No units available for siege"}

        # Calculate siege strength
        siege_strength = sum(unit["size"] * (unit["strength"] / 100) for unit in attacker_units)

        # Province defense based on development and fortifications
        province_defense = province.development * 10 + province.unrest * 5

        # Siege success chance
        siege_chance = siege_strength / (siege_strength + province_defense)
        siege_chance = max(0.1, min(0.9, siege_chance))  # Clamp between 10% and 90%

        if random.random() < siege_chance:
            # Successful siege
            original_owner = province.owner
            province.owner = attacker_country["name"]

            # Update country province lists
            if target_province in [p for p in provinces if p.owner == original_owner]:
                # Remove from original owner
                for country in [c for c in attacker_country.get("all_countries", []) if c["name"] == original_owner]:
                    if target_province in country.get("provinces", []):
                        country["provinces"].remove(target_province)

            # Add to new owner
            if target_province not in attacker_country.get("provinces", []):
                attacker_country.setdefault("provinces", []).append(target_province)

            return {
                "success": True,
                "message": f"Successfully captured {target_province}",
                "original_owner": original_owner,
                "new_owner": attacker_country["name"],
                "siege_strength": siege_strength,
                "province_defense": province_defense
            }
        else:
            # Failed siege
            return {
                "success": False,
                "message": f"Failed to capture {target_province}",
                "siege_strength": siege_strength,
                "province_defense": province_defense,
                "siege_chance": siege_chance
            }

    def _is_adjacent_province(self, province1, province2):
        """Check if two provinces are adjacent (simplified implementation)"""
        # This is a simplified implementation - in a real game you'd have a proper adjacency map
        return True  # For now, assume all provinces are adjacent

# Advanced AI System for Strategic Decision Making
class AIPersonality(BaseModel):
    name: str
    aggression: float  # 0.0-1.0, how likely to start wars
    expansion: float   # 0.0-1.0, how much they want to expand territory
    economic_focus: float  # 0.0-1.0, how much they prioritize economy
    diplomatic: float  # 0.0-1.0, how much they prefer diplomacy over force
    risk_tolerance: float  # 0.0-1.0, willingness to take risks
    trade_oriented: float  # 0.0-1.0, focus on trade and commerce

class AIDecision(BaseModel):
    country: str
    decision_type: str  # military, economic, diplomatic, internal
    action: str
    target: Optional[str] = None
    priority: float  # 0.0-1.0
    reasoning: str
    cost: float = 0.0
    expected_benefit: float = 0.0

class IntelligentAI:
    def __init__(self):
        self.personalities = self._initialize_ai_personalities()
        self.decision_history = {}  # country -> list of past decisions
        self.strategic_goals = {}   # country -> current strategic objectives
        self.threat_assessments = {}  # country -> threat analysis

    def _initialize_ai_personalities(self):
        """Create diverse AI personalities for different countries"""
        personalities = {
            "Astoria": AIPersonality(
                name="Astoria",
                aggression=0.3,
                expansion=0.7,
                economic_focus=0.8,
                diplomatic=0.6,
                risk_tolerance=0.4,
                trade_oriented=0.9
            ),
            "Belmont": AIPersonality(
                name="Belmont",
                aggression=0.8,
                expansion=0.9,
                economic_focus=0.4,
                diplomatic=0.2,
                risk_tolerance=0.7,
                trade_oriented=0.3
            ),
            "Cordova": AIPersonality(
                name="Cordova",
                aggression=0.2,
                expansion=0.3,
                economic_focus=0.9,
                diplomatic=0.8,
                risk_tolerance=0.2,
                trade_oriented=0.8
            ),
            "Drakmoor": AIPersonality(
                name="Drakmoor",
                aggression=0.9,
                expansion=0.8,
                economic_focus=0.3,
                diplomatic=0.1,
                risk_tolerance=0.9,
                trade_oriented=0.2
            ),
            "Eldoria": AIPersonality(
                name="Eldoria",
                aggression=0.4,
                expansion=0.5,
                economic_focus=0.7,
                diplomatic=0.7,
                risk_tolerance=0.5,
                trade_oriented=0.6
            )
        }
        return personalities

    def make_strategic_decisions(self, countries, provinces, game_state):
        """Main AI decision-making function called each turn"""
        ai_decisions = []

        for country in countries:
            country_name = country["name"]

            # Skip if this is the player's country (assuming first country is player)
            if country == countries[0]:
                continue

            # Get AI personality
            personality = self.personalities.get(country_name)
            if not personality:
                continue

            # Initialize decision tracking
            if country_name not in self.decision_history:
                self.decision_history[country_name] = []

            # Assess current situation
            situation = self._assess_situation(country, countries, provinces, game_state)

            # Update strategic goals
            self._update_strategic_goals(country, situation, personality)

            # Generate possible decisions
            possible_decisions = self._generate_possible_decisions(country, countries, provinces, personality, situation)

            # Evaluate and prioritize decisions
            prioritized_decisions = self._evaluate_decisions(possible_decisions, country, personality, situation)

            # Execute top priority decision
            if prioritized_decisions:
                chosen_decision = prioritized_decisions[0]
                execution_result = self._execute_decision(chosen_decision, countries, provinces, game_state)

                if execution_result["success"]:
                    ai_decisions.append(chosen_decision)
                    self.decision_history[country_name].append(chosen_decision)
                    logging.info(f"AI {country_name}: {chosen_decision.action} - {chosen_decision.reasoning}")

        return ai_decisions

    def _assess_situation(self, country, countries, provinces, game_state):
        """Assess the current strategic situation for a country"""
        country_name = country["name"]

        # Economic assessment
        economic_strength = self._calculate_economic_strength(country, provinces)

        # Military assessment
        military_strength = self._calculate_military_strength(country)

        # Territorial assessment
        territorial_control = len(country.get("provinces", []))

        # Threat assessment
        threats = self._assess_threats(country, countries)

        # Opportunity assessment
        opportunities = self._assess_opportunities(country, countries, provinces)

        return {
            "economic_strength": economic_strength,
            "military_strength": military_strength,
            "territorial_control": territorial_control,
            "threats": threats,
            "opportunities": opportunities,
            "stability": country.get("stability", 50),
            "treasury": country.get("treasury", 0),
            "war_exhaustion": country.get("war_exhaustion", 0)
        }

    def _calculate_economic_strength(self, country, provinces):
        """Calculate relative economic strength"""
        country_provinces = [p for p in provinces if p.owner == country["name"]]

        # Base economic factors
        treasury_factor = min(country.get("treasury", 0) / 1000, 2.0)  # Cap at 2.0
        income_factor = min(country.get("monthly_income", 0) / 100, 2.0)
        trade_factor = country.get("trade_efficiency", 0.5) * 2

        # Provincial development
        development_factor = sum(p.development for p in country_provinces) / 100

        # Resource wealth
        resource_factor = 0
        for province in country_provinces:
            for resource, amount in province.resources.items():
                resource_factor += amount * 0.1

        economic_strength = (treasury_factor + income_factor + trade_factor +
                           development_factor + resource_factor) / 5

        return min(economic_strength, 2.0)  # Cap at 2.0

    def _calculate_military_strength(self, country):
        """Calculate relative military strength"""
        military_units = country.get("military_units", [])

        if not military_units:
            return 0.1  # Minimal strength if no units

        # Calculate total effective strength
        total_strength = 0
        for unit in military_units:
            unit_strength = (unit["size"] / 1000) * (unit["strength"] / 100) * (unit["morale"] / 100)
            total_strength += unit_strength

        # Factor in military tradition
        tradition_bonus = 1 + (country.get("military_tradition", 0) / 100)

        # Factor in war exhaustion penalty
        exhaustion_penalty = 1 - (country.get("war_exhaustion", 0) / 200)

        military_strength = total_strength * tradition_bonus * exhaustion_penalty

        return min(military_strength, 3.0)  # Cap at 3.0

    def _assess_threats(self, country, countries):
        """Assess military and economic threats from other countries"""
        threats = []
        country_name = country["name"]

        for other_country in countries:
            if other_country["name"] == country_name:
                continue

            # Check for existing wars
            is_at_war = any(war.get("defender") == country_name or war.get("aggressor") == country_name
                          for war in other_country.get("wars", []))

            # Calculate relative strength
            other_military = self._calculate_military_strength(other_country)
            our_military = self._calculate_military_strength(country)

            # Calculate threat level
            threat_level = 0.0

            if is_at_war:
                threat_level += 0.8

            # Military threat
            if other_military > our_military * 1.5:
                threat_level += 0.4

            # Proximity threat (simplified - assume all countries are neighbors)
            threat_level += 0.1

            # Diplomatic relations
            relations = country.get("relations", {}).get(other_country["name"], 0)
            if relations < -20:
                threat_level += 0.3
            elif relations < 0:
                threat_level += 0.1

            if threat_level > 0.2:  # Only consider significant threats
                threats.append({
                    "country": other_country["name"],
                    "threat_level": threat_level,
                    "military_strength": other_military,
                    "is_at_war": is_at_war,
                    "relations": relations
                })

        # Sort by threat level
        threats.sort(key=lambda x: x["threat_level"], reverse=True)
        return threats[:3]  # Return top 3 threats

    def _assess_opportunities(self, country, countries, provinces):
        """Assess expansion and economic opportunities"""
        opportunities = []
        country_name = country["name"]

        for other_country in countries:
            if other_country["name"] == country_name:
                continue

            # Military opportunity (weak neighbors)
            our_military = self._calculate_military_strength(country)
            their_military = self._calculate_military_strength(other_country)

            if our_military > their_military * 1.3:  # We're significantly stronger
                opportunity_value = (our_military - their_military) * 0.5

                # Consider their wealth
                their_wealth = other_country.get("treasury", 0) + len(other_country.get("provinces", []))
                opportunity_value += their_wealth * 0.001

                opportunities.append({
                    "type": "military_expansion",
                    "target": other_country["name"],
                    "value": opportunity_value,
                    "description": f"Military conquest of {other_country['name']}"
                })

            # Trade opportunities
            trade_value = self._calculate_trade_opportunity_value(country, other_country, provinces)
            if trade_value > 0.3:
                opportunities.append({
                    "type": "trade_agreement",
                    "target": other_country["name"],
                    "value": trade_value,
                    "description": f"Trade agreement with {other_country['name']}"
                })

        # Sort by value
        opportunities.sort(key=lambda x: x["value"], reverse=True)
        return opportunities[:5]  # Return top 5 opportunities

    def _calculate_trade_opportunity_value(self, country1, country2, provinces):
        """Calculate the value of a potential trade agreement"""
        country1_provinces = [p for p in provinces if p.owner == country1["name"]]
        country2_provinces = [p for p in provinces if p.owner == country2["name"]]

        trade_value = 0.0

        # Check for complementary resources
        country1_resources = {}
        country2_resources = {}

        for province in country1_provinces:
            for resource, amount in province.resources.items():
                country1_resources[resource] = country1_resources.get(resource, 0) + amount

        for province in country2_provinces:
            for resource, amount in province.resources.items():
                country2_resources[resource] = country2_resources.get(resource, 0) + amount

        # Calculate complementarity
        for resource in country1_resources:
            if resource in country2_resources:
                surplus1 = max(0, country1_resources[resource] - 1.0)
                surplus2 = max(0, country2_resources[resource] - 1.0)

                if surplus1 > 0 and country2_resources[resource] < 1.0:
                    trade_value += surplus1 * 0.2
                if surplus2 > 0 and country1_resources[resource] < 1.0:
                    trade_value += surplus2 * 0.2

        return min(trade_value, 2.0)

    def _update_strategic_goals(self, country, situation, personality):
        """Update long-term strategic goals based on current situation"""
        country_name = country["name"]

        if country_name not in self.strategic_goals:
            self.strategic_goals[country_name] = []

        goals = []

        # Economic goals
        if situation["economic_strength"] < 1.0 and personality.economic_focus > 0.5:
            goals.append({
                "type": "economic_growth",
                "priority": personality.economic_focus,
                "description": "Focus on economic development and trade"
            })

        # Military goals
        if situation["military_strength"] < 1.0 and personality.aggression > 0.4:
            goals.append({
                "type": "military_buildup",
                "priority": personality.aggression,
                "description": "Strengthen military forces"
            })

        # Expansion goals
        if len(situation["opportunities"]) > 0 and personality.expansion > 0.5:
            goals.append({
                "type": "territorial_expansion",
                "priority": personality.expansion,
                "description": "Expand territorial control"
            })

        # Defensive goals
        if len(situation["threats"]) > 0:
            threat_priority = min(sum(t["threat_level"] for t in situation["threats"]), 1.0)
            goals.append({
                "type": "defense",
                "priority": threat_priority,
                "description": "Defend against threats"
            })

        self.strategic_goals[country_name] = goals

    def _generate_possible_decisions(self, country, countries, provinces, personality, situation):
        """Generate all possible decisions for the AI to consider"""
        decisions = []
        country_name = country["name"]

        # Military decisions
        if country.get("treasury", 0) > 200:
            # Recruit military units
            decisions.append(AIDecision(
                country=country_name,
                decision_type="military",
                action="recruit_units",
                priority=personality.aggression * 0.8,
                reasoning="Strengthen military forces",
                cost=200,
                expected_benefit=0.3
            ))

        # Economic decisions
        if country.get("treasury", 0) > 300:
            # Market manipulation
            for resource_name in ["grain", "iron", "textiles"]:
                decisions.append(AIDecision(
                    country=country_name,
                    decision_type="economic",
                    action="manipulate_market",
                    target=resource_name,
                    priority=personality.economic_focus * 0.6,
                    reasoning=f"Manipulate {resource_name} market for economic advantage",
                    cost=300,
                    expected_benefit=0.4
                ))

        # Diplomatic decisions
        for opportunity in situation["opportunities"]:
            if opportunity["type"] == "trade_agreement":
                decisions.append(AIDecision(
                    country=country_name,
                    decision_type="diplomatic",
                    action="establish_trade_agreement",
                    target=opportunity["target"],
                    priority=personality.trade_oriented * opportunity["value"],
                    reasoning=f"Establish profitable trade with {opportunity['target']}",
                    cost=0,
                    expected_benefit=opportunity["value"]
                ))

        # Aggressive decisions
        if personality.aggression > 0.6 and situation["military_strength"] > 1.2:
            for opportunity in situation["opportunities"]:
                if opportunity["type"] == "military_expansion":
                    # Check if we're not already at war
                    current_wars = country.get("wars", [])
                    if len(current_wars) == 0 or personality.risk_tolerance > 0.7:
                        decisions.append(AIDecision(
                            country=country_name,
                            decision_type="military",
                            action="declare_war",
                            target=opportunity["target"],
                            priority=personality.aggression * opportunity["value"],
                            reasoning=f"Conquer weak neighbor {opportunity['target']}",
                            cost=100,  # War exhaustion cost
                            expected_benefit=opportunity["value"]
                        ))

        # Defensive decisions
        for threat in situation["threats"]:
            if threat["threat_level"] > 0.5:
                # Build up defenses
                decisions.append(AIDecision(
                    country=country_name,
                    decision_type="military",
                    action="defensive_buildup",
                    target=threat["country"],
                    priority=threat["threat_level"] * 0.9,
                    reasoning=f"Defend against threat from {threat['country']}",
                    cost=250,
                    expected_benefit=threat["threat_level"]
                ))

        # Event response decisions (for AI countries with active events)
        active_events = []  # This would be populated from the event system
        for event in active_events:
            # AI chooses events based on personality
            best_choice = self._choose_event_response(event, personality)
            if best_choice:
                decisions.append(AIDecision(
                    country=country_name,
                    decision_type="internal",
                    action="respond_to_event",
                    target=event["id"],
                    priority=0.8,
                    reasoning=f"Respond to event: {event['title']}",
                    cost=0,
                    expected_benefit=0.5
                ))

        return decisions

    def _choose_event_response(self, event, personality):
        """Choose the best event response based on AI personality"""
        if not event.get("choices"):
            return None

        best_choice = None
        best_score = -1

        for choice in event["choices"]:
            score = 0

            # Score based on personality alignment
            if "military" in choice.get("text", "").lower():
                score += personality.aggression * 0.5
            if "trade" in choice.get("text", "").lower():
                score += personality.trade_oriented * 0.5
            if "diplomatic" in choice.get("text", "").lower():
                score += personality.diplomatic * 0.5
            if "economic" in choice.get("text", "").lower():
                score += personality.economic_focus * 0.5

            # Consider immediate effects
            effects = choice.get("immediate_effects", {})
            if "treasury" in effects:
                if effects["treasury"] > 0:
                    score += personality.economic_focus * 0.3
                else:
                    score -= (1 - personality.risk_tolerance) * 0.3

            if score > best_score:
                best_score = score
                best_choice = choice

        return best_choice

    def _evaluate_decisions(self, decisions, country, personality, situation):
        """Evaluate and prioritize decisions based on current situation"""
        if not decisions:
            return []

        # Calculate final priority scores
        for decision in decisions:
            # Base priority from decision
            final_priority = decision.priority

            # Adjust based on current situation
            if decision.decision_type == "military":
                if situation["war_exhaustion"] > 50:
                    final_priority *= 0.5  # Reduce military actions when exhausted
                if situation["threats"]:
                    final_priority *= 1.3  # Increase when threatened

            elif decision.decision_type == "economic":
                if situation["economic_strength"] < 0.8:
                    final_priority *= 1.4  # Prioritize economy when weak
                if situation["treasury"] < 200:
                    final_priority *= 1.2  # Prioritize when poor

            elif decision.decision_type == "diplomatic":
                if len(situation["threats"]) > 1:
                    final_priority *= 1.3  # Diplomacy more important when many threats

            # Risk tolerance adjustment
            risk_factor = decision.cost / max(country.get("treasury", 1), 1)
            if risk_factor > personality.risk_tolerance:
                final_priority *= 0.6  # Reduce priority for risky decisions

            # Benefit vs cost ratio
            if decision.cost > 0:
                benefit_ratio = decision.expected_benefit / (decision.cost / 100)
                final_priority *= min(benefit_ratio, 2.0)

            decision.priority = final_priority

        # Sort by priority and return top decisions
        decisions.sort(key=lambda d: d.priority, reverse=True)
        return decisions[:3]  # Return top 3 decisions

    def _execute_decision(self, decision, countries, provinces, game_state):
        """Execute an AI decision"""
        try:
            if decision.action == "recruit_units":
                return self._execute_recruit_units(decision, countries)

            elif decision.action == "manipulate_market":
                return self._execute_market_manipulation(decision, countries, game_state)

            elif decision.action == "establish_trade_agreement":
                return self._execute_trade_agreement(decision, countries, game_state)

            elif decision.action == "declare_war":
                return self._execute_declare_war(decision, countries, game_state)

            elif decision.action == "defensive_buildup":
                return self._execute_defensive_buildup(decision, countries)

            elif decision.action == "respond_to_event":
                return self._execute_event_response(decision, countries, game_state)

            else:
                return {"success": False, "error": f"Unknown action: {decision.action}"}

        except Exception as e:
            logging.error(f"Error executing AI decision {decision.action}: {e}")
            return {"success": False, "error": str(e)}

    def _execute_recruit_units(self, decision, countries):
        """Execute unit recruitment decision"""
        country = None
        for c in countries:
            if c["name"] == decision.country:
                country = c
                break

        if not country or country.get("treasury", 0) < decision.cost:
            return {"success": False, "error": "Insufficient funds"}

        # Deduct cost
        country["treasury"] -= decision.cost

        # Add military unit (simplified)
        if "military_units" not in country:
            country["military_units"] = []

        # Choose unit type based on current needs
        unit_type = "infantry"  # Default
        if len(country["military_units"]) > 3:
            unit_type = "cavalry"  # Add variety

        new_unit = {
            "name": f"{country['name']} {unit_type.title()} #{len(country['military_units']) + 1}",
            "unit_type": unit_type,
            "size": 1000,
            "strength": 100.0,
            "experience": 0.0,
            "morale": 100.0,
            "location": country.get("provinces", ["Capital"])[0],
            "maintenance_cost": 1.0
        }

        country["military_units"].append(new_unit)
        return {"success": True, "message": f"Recruited {unit_type} unit"}

    def _execute_market_manipulation(self, decision, countries, game_state):
        """Execute market manipulation decision"""
        country = None
        for c in countries:
            if c["name"] == decision.country:
                country = c
                break

        if not country or country.get("treasury", 0) < decision.cost:
            return {"success": False, "error": "Insufficient funds"}

        # Use the existing market manipulation system
        result = game_state.manipulate_market(decision.country, decision.target, "corner_market", decision.cost)
        return result

    def _execute_trade_agreement(self, decision, countries, game_state):
        """Execute trade agreement establishment"""
        # Use the existing trade agreement system
        result = game_state.establish_trade_agreement(decision.country, decision.target, "grain", {"trade_bonus": 0.1})
        return result

    def _execute_declare_war(self, decision, countries, game_state):
        """Execute war declaration"""
        aggressor = None
        target = None

        for c in countries:
            if c["name"] == decision.country:
                aggressor = c
            elif c["name"] == decision.target:
                target = c

        if not aggressor or not target:
            return {"success": False, "error": "Countries not found"}

        # Use the existing war declaration system
        result = game_state.military_system.declare_war(aggressor, target, "territorial_expansion")
        return {"success": True, "war_data": result}

    def _execute_defensive_buildup(self, decision, countries):
        """Execute defensive military buildup"""
        country = None
        for c in countries:
            if c["name"] == decision.country:
                country = c
                break

        if not country or country.get("treasury", 0) < decision.cost:
            return {"success": False, "error": "Insufficient funds"}

        # Deduct cost and improve defenses
        country["treasury"] -= decision.cost
        country["military_tradition"] = min(100, country.get("military_tradition", 0) + 5)

        # Improve existing units
        for unit in country.get("military_units", []):
            unit["strength"] = min(100, unit["strength"] + 10)
            unit["morale"] = min(100, unit["morale"] + 5)

        return {"success": True, "message": "Defensive buildup completed"}

    def _execute_event_response(self, decision, countries, game_state):
        """Execute event response decision"""
        # This would integrate with the event system to make AI choices
        # For now, return success
        return {"success": True, "message": "Event response executed"}

# Advanced Diplomatic Structures
class Alliance(BaseModel):
    id: str
    name: str
    members: List[str]  # Country names
    leader: str  # Leading country
    alliance_type: str  # defensive, offensive, trade, research
    established_turn: int
    terms: Dict[str, Any] = {}
    status: str = "active"  # active, dissolved, suspended

class Treaty(BaseModel):
    id: str
    name: str
    parties: List[str]  # Country names
    treaty_type: str  # peace, trade, non_aggression, mutual_defense
    terms: Dict[str, Any] = {}
    established_turn: int
    duration: int = -1  # -1 for permanent
    status: str = "active"

class DiplomaticAction(BaseModel):
    id: str
    initiator: str
    target: str
    action_type: str  # alliance_proposal, treaty_proposal, trade_embargo, etc.
    terms: Dict[str, Any] = {}
    status: str = "pending"  # pending, accepted, rejected, expired
    created_turn: int
    expires_turn: int = -1

class DiplomaticIncident(BaseModel):
    id: str
    countries_involved: List[str]
    incident_type: str  # border_dispute, trade_conflict, cultural_tension
    severity: float  # 0.0-1.0
    description: str
    turn_occurred: int
    resolved: bool = False

# Enhanced Diplomacy System with Complex Political Mechanics
class DiplomacySystem:
    def __init__(self):
        self.ai_system = IntelligentAI()
        self.alliances = {}  # alliance_id -> Alliance
        self.treaties = {}   # treaty_id -> Treaty
        self.diplomatic_actions = {}  # action_id -> DiplomaticAction
        self.diplomatic_incidents = {}  # incident_id -> DiplomaticIncident
        self.diplomatic_modifiers = {}  # country_pair -> list of modifiers
        self.espionage_networks = {}  # country -> {target_country: network_strength}

    def update(self, countries, provinces=None, game_state=None):
        """Update diplomacy system and process AI decisions"""
        # Process AI decisions for all non-player countries
        if game_state:
            ai_decisions = self.ai_system.make_strategic_decisions(countries, provinces or [], game_state)

            # Log AI actions for player awareness
            for decision in ai_decisions:
                logging.info(f"AI Action: {decision.country} - {decision.action} ({decision.reasoning})")

        # Update diplomatic actions and treaties
        self._update_diplomatic_actions(game_state.turn if game_state else 1)
        self._process_alliance_effects(countries)
        self._generate_diplomatic_incidents(countries, game_state.turn if game_state else 1)
        self._update_espionage_networks(countries)

    def _update_diplomatic_actions(self, current_turn):
        """Update status of pending diplomatic actions"""
        expired_actions = []

        for action_id, action in self.diplomatic_actions.items():
            if action.expires_turn != -1 and current_turn >= action.expires_turn:
                action.status = "expired"
                expired_actions.append(action_id)

        # Remove expired actions
        for action_id in expired_actions:
            del self.diplomatic_actions[action_id]

    def _process_alliance_effects(self, countries):
        """Process ongoing effects of alliances"""
        for alliance in self.alliances.values():
            if alliance.status != "active":
                continue

            # Find alliance members
            member_countries = []
            for country in countries:
                if country["name"] in alliance.members:
                    member_countries.append(country)

            # Apply alliance bonuses
            if alliance.alliance_type == "trade":
                # Trade alliance bonuses
                for country in member_countries:
                    country["trade_efficiency"] = min(1.0, country.get("trade_efficiency", 0.5) + 0.05)

            elif alliance.alliance_type == "research":
                # Research alliance bonuses
                for country in member_countries:
                    for category in country.get("research_points", {}):
                        country["research_points"][category] = country["research_points"].get(category, 0) + 1

            elif alliance.alliance_type == "defensive":
                # Defensive alliance bonuses
                for country in member_countries:
                    country["military_tradition"] = min(100, country.get("military_tradition", 0) + 1)

    def _generate_diplomatic_incidents(self, countries, current_turn):
        """Generate random diplomatic incidents that affect relations"""
        if random.random() < 0.1:  # 10% chance per turn
            # Select two random countries
            if len(countries) >= 2:
                country1, country2 = random.sample(countries, 2)

                incident_types = [
                    "border_dispute", "trade_conflict", "cultural_tension",
                    "fishing_rights", "diplomatic_insult", "spy_discovery"
                ]

                incident_type = random.choice(incident_types)
                severity = random.uniform(0.2, 0.8)

                incident = DiplomaticIncident(
                    id=f"incident_{current_turn}_{country1['name']}_{country2['name']}",
                    countries_involved=[country1["name"], country2["name"]],
                    incident_type=incident_type,
                    severity=severity,
                    description=self._generate_incident_description(incident_type, country1["name"], country2["name"]),
                    turn_occurred=current_turn
                )

                self.diplomatic_incidents[incident.id] = incident

                # Apply immediate relation penalty
                relation_penalty = -int(severity * 20)
                country1.setdefault("relations", {})[country2["name"]] = max(-100,
                    country1.get("relations", {}).get(country2["name"], 0) + relation_penalty)
                country2.setdefault("relations", {})[country1["name"]] = max(-100,
                    country2.get("relations", {}).get(country1["name"], 0) + relation_penalty)

                logging.info(f"Diplomatic incident: {incident.description}")

    def _generate_incident_description(self, incident_type, country1, country2):
        """Generate description for diplomatic incidents"""
        descriptions = {
            "border_dispute": f"Border tensions escalate between {country1} and {country2} over disputed territory",
            "trade_conflict": f"Trade dispute emerges between {country1} and {country2} over tariffs and trade routes",
            "cultural_tension": f"Cultural and religious differences cause friction between {country1} and {country2}",
            "fishing_rights": f"Fishing vessels from {country1} and {country2} clash over territorial waters",
            "diplomatic_insult": f"Diplomatic incident occurs when {country1} ambassador insults {country2} officials",
            "spy_discovery": f"{country2} discovers {country1} espionage network operating within their borders"
        }
        return descriptions.get(incident_type, f"Diplomatic incident between {country1} and {country2}")

    def _update_espionage_networks(self, countries):
        """Update espionage network strength and effects"""
        for country in countries:
            country_name = country["name"]

            if country_name not in self.espionage_networks:
                self.espionage_networks[country_name] = {}

            # Espionage networks decay over time without investment
            for target in list(self.espionage_networks[country_name].keys()):
                current_strength = self.espionage_networks[country_name][target]
                self.espionage_networks[country_name][target] = max(0, current_strength - 0.05)

                # Remove networks that become too weak
                if self.espionage_networks[country_name][target] < 0.1:
                    del self.espionage_networks[country_name][target]

    def update(self, countries, provinces=None, game_state=None):
        """Update diplomacy system and process AI decisions"""
        # Process AI decisions for all non-player countries
        if game_state:
            ai_decisions = self.ai_system.make_strategic_decisions(countries, provinces or [], game_state)

            # Log AI actions for player awareness
            for decision in ai_decisions:
                logging.info(f"AI Action: {decision.country} - {decision.action} ({decision.reasoning})")

    def get_actions(self, country_name=None):
        """Get available diplomatic actions for a country"""
        actions = [
            {"action": "Form Alliance", "target": "Select Country", "cost": 150, "description": "Create a formal alliance with another nation"},
            {"action": "Propose Treaty", "target": "Select Country", "cost": 100, "description": "Negotiate a formal treaty"},
            {"action": "Trade Embargo", "target": "Select Country", "cost": 75, "description": "Impose economic sanctions"},
            {"action": "Establish Embassy", "target": "Select Country", "cost": 200, "description": "Build diplomatic relations"},
            {"action": "Spy Network", "target": "Select Country", "cost": 300, "description": "Establish espionage operations"},
            {"action": "Cultural Exchange", "target": "Select Country", "cost": 125, "description": "Improve relations through culture"},
            {"action": "Military Access", "target": "Select Country", "cost": 50, "description": "Request passage rights"},
            {"action": "Improve Relations", "target": "Select Country", "cost": 75, "description": "General diplomatic outreach"},
            {"action": "Send Gift", "target": "Select Country", "cost": 200, "description": "Monetary gift to improve relations"}
        ]
        return actions

    def propose_alliance(self, initiator_name, target_name, alliance_type, terms, countries):
        """Propose an alliance between two countries"""
        initiator = None
        target = None

        for country in countries:
            if country["name"] == initiator_name:
                initiator = country
            elif country["name"] == target_name:
                target = country

        if not initiator or not target:
            return {"success": False, "error": "Countries not found"}

        # Check if countries can form alliance
        current_relations = initiator.get("relations", {}).get(target_name, 0)
        if current_relations < 20:
            return {"success": False, "error": "Relations too poor for alliance"}

        # Check if already in alliance together
        for alliance in self.alliances.values():
            if initiator_name in alliance.members and target_name in alliance.members:
                return {"success": False, "error": "Already in alliance together"}

        # Create alliance proposal
        action_id = f"alliance_{initiator_name}_{target_name}_{len(self.diplomatic_actions)}"
        proposal = DiplomaticAction(
            id=action_id,
            initiator=initiator_name,
            target=target_name,
            action_type="alliance_proposal",
            terms={
                "alliance_type": alliance_type,
                "terms": terms
            },
            created_turn=1,  # Should be current turn
            expires_turn=5   # Expires in 5 turns
        )

        self.diplomatic_actions[action_id] = proposal

        # AI auto-response based on relations and personality
        if self._should_ai_accept_alliance(target, initiator, alliance_type, current_relations):
            return self._accept_alliance_proposal(action_id, countries)

        return {"success": True, "message": f"Alliance proposal sent to {target_name}", "proposal_id": action_id}

    def _should_ai_accept_alliance(self, target_country, initiator_country, alliance_type, relations):
        """Determine if AI should accept alliance proposal"""
        # Get AI personality if available
        if hasattr(self.ai_system, 'personalities'):
            personality = self.ai_system.personalities.get(target_country["name"])
            if personality:
                acceptance_chance = 0.3  # Base chance

                # Relation bonus
                acceptance_chance += max(0, relations / 100)

                # Personality factors
                if alliance_type == "trade" and personality.trade_oriented > 0.6:
                    acceptance_chance += 0.3
                elif alliance_type == "defensive" and personality.diplomatic > 0.6:
                    acceptance_chance += 0.3
                elif alliance_type == "offensive" and personality.aggression > 0.7:
                    acceptance_chance += 0.2

                # Threat assessment
                threats = self.ai_system._assess_threats(target_country, [initiator_country])
                if len(threats) > 0:
                    acceptance_chance += 0.2  # More likely to ally when threatened

                return random.random() < acceptance_chance

        # Fallback: simple relation-based decision
        return relations > 40 and random.random() < 0.6

    def _accept_alliance_proposal(self, proposal_id, countries):
        """Accept an alliance proposal"""
        proposal = self.diplomatic_actions.get(proposal_id)
        if not proposal:
            return {"success": False, "error": "Proposal not found"}

        # Create alliance
        alliance_id = f"alliance_{len(self.alliances)}"
        alliance = Alliance(
            id=alliance_id,
            name=f"{proposal.initiator}-{proposal.target} Alliance",
            members=[proposal.initiator, proposal.target],
            leader=proposal.initiator,  # Initiator becomes leader
            alliance_type=proposal.terms["alliance_type"],
            established_turn=1,  # Should be current turn
            terms=proposal.terms["terms"]
        )

        self.alliances[alliance_id] = alliance

        # Update country data
        for country in countries:
            if country["name"] in alliance.members:
                if "alliances" not in country:
                    country["alliances"] = []
                country["alliances"].append(alliance_id)

                # Improve relations between alliance members
                for member in alliance.members:
                    if member != country["name"]:
                        country.setdefault("relations", {})[member] = min(100,
                            country.get("relations", {}).get(member, 0) + 25)

        # Remove the proposal
        proposal.status = "accepted"
        del self.diplomatic_actions[proposal_id]

        return {"success": True, "message": f"Alliance formed: {alliance.name}", "alliance": alliance}

    def establish_embassy(self, country1_name, country2_name, countries):
        """Establish an embassy between two countries"""
        country1 = None
        country2 = None

        for country in countries:
            if country["name"] == country1_name:
                country1 = country
            elif country["name"] == country2_name:
                country2 = country

        if not country1 or not country2:
            return {"success": False, "error": "Countries not found"}

        cost = 200
        if country1.get("treasury", 0) < cost:
            return {"success": False, "error": "Insufficient funds"}

        # Deduct cost
        country1["treasury"] -= cost

        # Add embassy
        if "embassies" not in country1:
            country1["embassies"] = []
        if "embassies" not in country2:
            country2["embassies"] = []

        if country2_name not in country1["embassies"]:
            country1["embassies"].append(country2_name)
        if country1_name not in country2["embassies"]:
            country2["embassies"].append(country1_name)

        # Improve relations and diplomatic reputation
        country1.setdefault("relations", {})[country2_name] = min(100,
            country1.get("relations", {}).get(country2_name, 0) + 20)
        country2.setdefault("relations", {})[country1_name] = min(100,
            country2.get("relations", {}).get(country1_name, 0) + 15)

        country1["diplomatic_reputation"] = min(100, country1.get("diplomatic_reputation", 0) + 5)

        return {"success": True, "message": f"Embassy established with {country2_name}"}

    def establish_spy_network(self, country1_name, country2_name, countries):
        """Establish espionage network in target country"""
        country1 = None
        country2 = None

        for country in countries:
            if country["name"] == country1_name:
                country1 = country
            elif country["name"] == country2_name:
                country2 = country

        if not country1 or not country2:
            return {"success": False, "error": "Countries not found"}

        cost = 300
        if country1.get("treasury", 0) < cost:
            return {"success": False, "error": "Insufficient funds"}

        # Deduct cost
        country1["treasury"] -= cost

        # Establish spy network
        if country1_name not in self.espionage_networks:
            self.espionage_networks[country1_name] = {}

        current_strength = self.espionage_networks[country1_name].get(country2_name, 0)
        self.espionage_networks[country1_name][country2_name] = min(1.0, current_strength + 0.3)

        # Chance of discovery
        discovery_chance = 0.2 - (country1.get("diplomatic_reputation", 0) / 500)
        if random.random() < discovery_chance:
            # Network discovered!
            country2.setdefault("relations", {})[country1_name] = max(-100,
                country2.get("relations", {}).get(country1_name, 0) - 30)

            # Create diplomatic incident
            incident = DiplomaticIncident(
                id=f"spy_discovery_{country1_name}_{country2_name}",
                countries_involved=[country1_name, country2_name],
                incident_type="spy_discovery",
                severity=0.7,
                description=f"{country2_name} discovers {country1_name} espionage network",
                turn_occurred=1
            )
            self.diplomatic_incidents[incident.id] = incident

            return {"success": True, "message": f"Spy network established but discovered by {country2_name}!", "discovered": True}

        return {"success": True, "message": f"Spy network established in {country2_name}"}

    def impose_trade_embargo(self, country1_name, country2_name, countries):
        """Impose trade embargo on target country"""
        country1 = None
        country2 = None

        for country in countries:
            if country["name"] == country1_name:
                country1 = country
            elif country["name"] == country2_name:
                country2 = country

        if not country1 or not country2:
            return {"success": False, "error": "Countries not found"}

        cost = 75
        if country1.get("treasury", 0) < cost:
            return {"success": False, "error": "Insufficient funds"}

        # Deduct cost
        country1["treasury"] -= cost

        # Add embargo
        if "trade_embargoes" not in country1:
            country1["trade_embargoes"] = []
        if "embargoed_by" not in country2:
            country2["embargoed_by"] = []

        if country2_name not in country1["trade_embargoes"]:
            country1["trade_embargoes"].append(country2_name)
        if country1_name not in country2["embargoed_by"]:
            country2["embargoed_by"].append(country1_name)

        # Reduce trade efficiency for both countries
        country1["trade_efficiency"] = max(0.1, country1.get("trade_efficiency", 0.5) - 0.1)
        country2["trade_efficiency"] = max(0.1, country2.get("trade_efficiency", 0.5) - 0.15)

        # Worsen relations
        country1.setdefault("relations", {})[country2_name] = max(-100,
            country1.get("relations", {}).get(country2_name, 0) - 20)
        country2.setdefault("relations", {})[country1_name] = max(-100,
            country2.get("relations", {}).get(country1_name, 0) - 25)

        return {"success": True, "message": f"Trade embargo imposed on {country2_name}"}

    def cultural_exchange(self, country1_name, country2_name, countries):
        """Establish cultural exchange program"""
        country1 = None
        country2 = None

        for country in countries:
            if country["name"] == country1_name:
                country1 = country
            elif country["name"] == country2_name:
                country2 = country

        if not country1 or not country2:
            return {"success": False, "error": "Countries not found"}

        cost = 125
        if country1.get("treasury", 0) < cost:
            return {"success": False, "error": "Insufficient funds"}

        # Deduct cost
        country1["treasury"] -= cost

        # Add cultural exchange
        if "cultural_exchanges" not in country1:
            country1["cultural_exchanges"] = []
        if "cultural_exchanges" not in country2:
            country2["cultural_exchanges"] = []

        if country2_name not in country1["cultural_exchanges"]:
            country1["cultural_exchanges"].append(country2_name)
        if country1_name not in country2["cultural_exchanges"]:
            country2["cultural_exchanges"].append(country1_name)

        # Improve relations and stability
        country1.setdefault("relations", {})[country2_name] = min(100,
            country1.get("relations", {}).get(country2_name, 0) + 15)
        country2.setdefault("relations", {})[country1_name] = min(100,
            country2.get("relations", {}).get(country1_name, 0) + 15)

        country1["stability"] = min(100, country1.get("stability", 50) + 5)
        country2["stability"] = min(100, country2.get("stability", 50) + 5)

        return {"success": True, "message": f"Cultural exchange established with {country2_name}"}

    def perform_action(self, countries, country_name, action, target):
        """Perform a diplomatic action"""
        country = None
        target_country = None

        for c in countries:
            if c["name"] == country_name:
                country = c
            elif c["name"] == target:
                target_country = c

        if not country or not target_country:
            return {"success": False, "error": "Country not found"}

        # Process different diplomatic actions
        if action == "Form Alliance":
            return self.propose_alliance(country_name, target, "defensive", {}, countries)

        elif action == "Establish Embassy":
            return self.establish_embassy(country_name, target, countries)

        elif action == "Spy Network":
            return self.establish_spy_network(country_name, target, countries)

        elif action == "Trade Embargo":
            return self.impose_trade_embargo(country_name, target, countries)

        elif action == "Cultural Exchange":
            return self.cultural_exchange(country_name, target, countries)

        elif action == "Improve Relations":
            cost = 75
            if country.get("treasury", 0) < cost:
                return {"success": False, "error": "Insufficient funds"}

            country["treasury"] -= cost

            # Improve relations
            if "relations" not in country:
                country["relations"] = {}
            if "relations" not in target_country:
                target_country["relations"] = {}

            current_relation = country["relations"].get(target, 0)
            country["relations"][target] = min(100, current_relation + 15)
            target_country["relations"][country_name] = min(100, target_country["relations"].get(country_name, 0) + 10)

            return {"success": True, "message": f"Relations with {target} improved"}

        elif action == "Send Gift":
            cost = 200
            if country.get("treasury", 0) < cost:
                return {"success": False, "error": "Insufficient funds"}

            country["treasury"] -= cost
            target_country["treasury"] = target_country.get("treasury", 0) + 100

            # Improve relations significantly
            if "relations" not in country:
                country["relations"] = {}
            if "relations" not in target_country:
                target_country["relations"] = {}

            current_relation = country["relations"].get(target, 0)
            country["relations"][target] = min(100, current_relation + 25)
            target_country["relations"][country_name] = min(100, target_country["relations"].get(country_name, 0) + 20)

            return {"success": True, "message": f"Gift sent to {target}"}

        else:
            return {"success": False, "error": f"Action '{action}' not implemented yet"}

class GameManager:
    def __init__(self):
        logging.debug("Initializing GameManager...")
        self.turn = 1
        self.map_manager = MapManager()

        # Initialize sophisticated game systems
        self.resource_manager = ResourceManager()
        self.pop_system = PopSystem()
        self.economy_system = EconomySystem(self.resource_manager)
        self.military_system = MilitarySystem()
        self.diplomacy_system = DiplomacySystem()
        self.event_system = EventSystem()

        # Initialize technologies
        self.technologies = self._initialize_technologies()

        logging.debug("GameManager initialized with advanced systems.")

    def _initialize_technologies(self):
        """Initialize the comprehensive technology tree"""
        technologies = [
            # Early Economic Technologies
            Technology(
                name="Agriculture", category="economic", cost=50,
                prerequisites=[],
                effects={"farm_efficiency": 0.2, "population_growth": 0.01},
                description="Improved farming techniques increase food production and support larger populations"
            ),
            Technology(
                name="Animal Husbandry", category="economic", cost=60,
                prerequisites=["Agriculture"],
                effects={"farm_efficiency": 0.15, "cavalry_strength": 0.1},
                description="Domestication of animals improves farming and provides cavalry mounts"
            ),
            Technology(
                name="Currency", category="economic", cost=100,
                prerequisites=["Agriculture"],
                effects={"trade_efficiency": 0.25, "tax_efficiency": 0.1},
                description="Standardized currency facilitates trade and taxation"
            ),
            Technology(
                name="Banking", category="economic", cost=200,
                prerequisites=["Currency"],
                effects={"inflation_reduction": 0.02, "loan_interest": -0.01, "trade_efficiency": 0.15},
                description="Banking systems stabilize the economy and enable complex financial instruments"
            ),
            Technology(
                name="Mercantilism", category="economic", cost=300,
                prerequisites=["Banking"],
                effects={"trade_efficiency": 0.3, "colonial_income": 0.2},
                description="State-controlled trade policies maximize national wealth"
            ),

            # Early Military Technologies
            Technology(
                name="Iron Working", category="military", cost=75,
                prerequisites=[],
                effects={"military_unit_strength": 0.15, "mine_efficiency": 0.1},
                description="Advanced metallurgy improves weapons, tools, and armor"
            ),
            Technology(
                name="Military Engineering", category="military", cost=120,
                prerequisites=["Iron Working"],
                effects={"siege_ability": 0.2, "fortification_defense": 0.15},
                description="Engineering knowledge improves siege warfare and fortifications"
            ),
            Technology(
                name="Professional Army", category="military", cost=150,
                prerequisites=["Iron Working"],
                effects={"military_tradition_gain": 0.5, "army_maintenance": -0.1, "unit_morale": 0.1},
                description="Professional soldiers are more effective, disciplined, and loyal"
            ),
            Technology(
                name="Gunpowder", category="military", cost=250,
                prerequisites=["Military Engineering"],
                effects={"artillery_strength": 0.3, "infantry_strength": 0.2, "siege_ability": 0.25},
                description="Explosive powder revolutionizes warfare with firearms and cannons"
            ),
            Technology(
                name="Naval Artillery", category="military", cost=300,
                prerequisites=["Gunpowder"],
                effects={"naval_strength": 0.4, "coastal_defense": 0.2},
                description="Ship-mounted cannons dominate naval warfare"
            ),

            # Social Technologies
            Technology(
                name="Writing", category="social", cost=40,
                prerequisites=[],
                effects={"research_efficiency": 0.15, "administrative_efficiency": 0.1},
                description="Written language enables record-keeping and knowledge preservation"
            ),
            Technology(
                name="Education", category="social", cost=120,
                prerequisites=["Writing"],
                effects={"research_efficiency": 0.25, "pop_consciousness": 0.1, "innovation_chance": 0.05},
                description="Formal education systems increase literacy and innovation"
            ),
            Technology(
                name="Printing Press", category="social", cost=200,
                prerequisites=["Education"],
                effects={"research_efficiency": 0.3, "pop_consciousness": 0.15, "cultural_conversion": 0.1},
                description="Mass production of books spreads knowledge and ideas rapidly"
            ),
            Technology(
                name="Scientific Method", category="social", cost=350,
                prerequisites=["Printing Press"],
                effects={"research_efficiency": 0.4, "innovation_chance": 0.15, "technology_cost": -0.1},
                description="Systematic approach to knowledge accelerates scientific progress"
            ),

            # Administrative Technologies
            Technology(
                name="Code of Laws", category="administrative", cost=80,
                prerequisites=["Writing"],
                effects={"stability_bonus": 0.1, "tax_efficiency": 0.15, "unrest_reduction": 0.1},
                description="Written legal codes provide consistent justice and order"
            ),
            Technology(
                name="Bureaucracy", category="administrative", cost=150,
                prerequisites=["Code of Laws"],
                effects={"administrative_efficiency": 0.2, "corruption_reduction": 0.15, "province_limit": 2},
                description="Professional civil service improves government efficiency"
            ),
            Technology(
                name="Centralization", category="administrative", cost=250,
                prerequisites=["Bureaucracy"],
                effects={"tax_efficiency": 0.2, "stability_bonus": 0.15, "revolt_risk": -0.1},
                description="Centralized authority strengthens state control and unity"
            ),
            Technology(
                name="Enlightenment", category="administrative", cost=400,
                prerequisites=["Centralization", "Scientific Method"],
                effects={"research_efficiency": 0.3, "diplomatic_reputation": 0.2, "reform_desire": 0.2},
                description="Rational thinking and human rights transform governance"
            ),

            # Advanced Technologies
            Technology(
                name="Steam Engine", category="economic", cost=500,
                prerequisites=["Scientific Method", "Iron Working"],
                effects={"production_efficiency": 0.4, "military_mobility": 0.2, "trade_range": 0.3},
                description="Steam power revolutionizes industry and transportation"
            ),
            Technology(
                name="Industrialization", category="economic", cost=600,
                prerequisites=["Steam Engine"],
                effects={"production_efficiency": 0.5, "urban_growth": 0.3, "pollution": 0.2},
                description="Factory system transforms economy but creates new social challenges"
            ),
            Technology(
                name="Railroad", category="economic", cost=700,
                prerequisites=["Steam Engine"],
                effects={"trade_efficiency": 0.4, "military_mobility": 0.3, "national_unity": 0.2},
                description="Rail networks unite nations and accelerate economic growth"
            ),
            Technology(
                name="Nationalism", category="social", cost=450,
                prerequisites=["Enlightenment"],
                effects={"military_morale": 0.2, "cultural_unity": 0.3, "revolt_risk": 0.1},
                description="National identity strengthens unity but can fuel conflicts"
            )
        ]
        return {tech.name: tech for tech in technologies}

    def advance_turn(self):
        logging.debug(f"Advancing turn from {self.turn}")
        self.turn += 1

        # Update all game systems with the new sophisticated mechanics
        self.pop_system.update(self.map_manager.countries, self.map_manager.provinces)
        self.economy_system.update(self.map_manager.countries, self.map_manager.provinces)
        self.military_system.update(self.map_manager.countries, self.map_manager.provinces)
        self.diplomacy_system.update(self.map_manager.countries, self.map_manager.provinces, self)
        self.event_system.update(self.map_manager.countries, self.map_manager.provinces)

        # Process technology research
        self._process_research()

        # Update country relationships
        self._update_diplomacy()

        # Update market dynamics and trade competition
        self._update_market_dynamics()

        logging.debug(f"Turn advanced to {self.turn}")

        # Return enhanced turn data
        return {
            "turn": self.turn,
            "countries": self.map_manager.countries,
            "provinces": [p.model_dump() for p in self.map_manager.provinces],
            "global_resources": {name: res.model_dump() for name, res in self.resource_manager.global_resources.items()}
        }

    def _process_research(self):
        """Process technology research for all countries"""
        for country in self.map_manager.countries:
            # Generate research points based on country development
            base_research = len(country["provinces"]) * 0.5

            # Add research points
            for category in country["research_points"]:
                country["research_points"][category] += base_research

                # Check if current research is complete
                current_tech = country["current_research"].get(category)
                if current_tech and current_tech in self.technologies:
                    tech = self.technologies[current_tech]
                    if country["research_points"][category] >= tech.cost:
                        # Research complete!
                        country["technologies"].append(current_tech)
                        country["research_points"][category] -= tech.cost
                        country["current_research"][category] = ""

                        # Apply technology effects
                        for effect, value in tech.effects.items():
                            country["national_modifiers"][effect] = country["national_modifiers"].get(effect, 0) + value

                        logging.info(f"{country['name']} completed research: {current_tech}")

    def _update_diplomacy(self):
        """Update diplomatic relationships between countries"""
        countries = self.map_manager.countries

        for i, country1 in enumerate(countries):
            for j, country2 in enumerate(countries[i+1:], i+1):
                # Initialize relations if not exist
                if country2["name"] not in country1["relations"]:
                    country1["relations"][country2["name"]] = 0.0
                if country1["name"] not in country2["relations"]:
                    country2["relations"][country1["name"]] = 0.0

                # Gradual relation changes based on various factors
                relation_change = 0.0

                # Trade relations improve relations
                if self._countries_trade(country1, country2):
                    relation_change += 1.0

                # Similar government types improve relations
                if country1["government_type"] == country2["government_type"]:
                    relation_change += 0.5

                # Random diplomatic events
                relation_change += random.uniform(-0.5, 0.5)

                # Apply changes
                country1["relations"][country2["name"]] = max(-100, min(100,
                    country1["relations"][country2["name"]] + relation_change))
                country2["relations"][country1["name"]] = country1["relations"][country2["name"]]

    def _countries_trade(self, country1, country2):
        """Check if two countries have trade relations"""
        # Simple check - look for trade routes between their provinces
        country1_provinces = [p.name for p in self.map_manager.provinces if p.owner == country1["name"]]
        country2_provinces = [p.name for p in self.map_manager.provinces if p.owner == country2["name"]]

        for province in self.map_manager.provinces:
            if province.name in country1_provinces:
                for trade_route in province.trade_routes:
                    if trade_route.to_province in country2_provinces:
                        return True
        return False

    def _update_market_dynamics(self):
        """Update global market dynamics and trade competition"""
        # Update resource prices based on supply and demand
        for resource_name, resource in self.resource_manager.global_resources.items():
            # Calculate price based on supply/demand ratio
            if resource.supply > 0:
                supply_demand_ratio = resource.demand / resource.supply

                # Price adjustment based on market forces
                price_change_factor = 1.0
                if supply_demand_ratio > 1.2:  # High demand, low supply
                    price_change_factor = 1.1  # 10% price increase
                elif supply_demand_ratio < 0.8:  # Low demand, high supply
                    price_change_factor = 0.9  # 10% price decrease

                # Apply gradual price changes
                new_price = resource.current_price * price_change_factor
                resource.current_price = max(resource.base_value * 0.3,
                                           min(resource.base_value * 3.0, new_price))

        # Create trade opportunities and competition
        self._create_trade_opportunities()

        # Update trade route efficiency based on competition
        self._update_trade_competition()

    def _create_trade_opportunities(self):
        """Create new trade opportunities based on resource availability and demand"""
        # Find provinces with resource surpluses and deficits
        resource_surplus = {}  # resource -> [(province, surplus_amount)]
        resource_deficit = {}  # resource -> [(province, deficit_amount)]

        for province in self.map_manager.provinces:
            for resource_name, amount in province.resources.items():
                # Calculate local consumption vs production
                local_consumption = self._calculate_local_consumption(province, resource_name)
                surplus = amount - local_consumption

                if surplus > 0.5:  # Significant surplus
                    if resource_name not in resource_surplus:
                        resource_surplus[resource_name] = []
                    resource_surplus[resource_name].append((province, surplus))
                elif surplus < -0.3:  # Significant deficit
                    if resource_name not in resource_deficit:
                        resource_deficit[resource_name] = []
                    resource_deficit[resource_name].append((province, abs(surplus)))

        # Create new trade routes where profitable
        for resource_name in resource_surplus:
            if resource_name in resource_deficit:
                surplus_provinces = resource_surplus[resource_name]
                deficit_provinces = resource_deficit[resource_name]

                # Try to create profitable trade routes
                for surplus_province, surplus_amount in surplus_provinces:
                    for deficit_province, deficit_amount in deficit_provinces:
                        if surplus_province.owner != deficit_province.owner:
                            # Check if trade route would be profitable
                            if self._is_trade_route_profitable(surplus_province, deficit_province, resource_name):
                                self._create_trade_route(surplus_province, deficit_province, resource_name,
                                                       min(surplus_amount, deficit_amount))

    def _calculate_local_consumption(self, province, resource_name):
        """Calculate local consumption of a resource in a province"""
        base_consumption = {
            "grain": 0.5,  # Food consumption
            "textiles": 0.2,  # Clothing
            "luxury_goods": 0.1,  # Luxury consumption
            "iron": 0.3,  # Tools and weapons
            "coal": 0.4,  # Energy
            "timber": 0.3,  # Construction
            "fish": 0.3   # Food
        }

        # Scale by population and development
        total_population = sum(pop.size for pop in province.population_groups)
        population_factor = total_population / 50000  # Normalize to base population
        development_factor = province.development / 50  # Normalize to base development

        return base_consumption.get(resource_name, 0.2) * population_factor * development_factor

    def _is_trade_route_profitable(self, from_province, to_province, resource_name):
        """Check if a trade route would be profitable"""
        resource = self.resource_manager.global_resources.get(resource_name)
        if not resource:
            return False

        # Calculate distance penalty (simplified)
        distance_penalty = 0.9  # 10% efficiency loss for distance

        # Calculate potential profit
        base_profit = resource.current_price * 0.3  # 30% markup
        transport_cost = resource.current_price * 0.1  # 10% transport cost

        net_profit = (base_profit - transport_cost) * distance_penalty

        return net_profit > 0.05  # Minimum 5% profit margin

    def _create_trade_route(self, from_province, to_province, resource_name, volume):
        """Create a new trade route between provinces"""
        # Check if route already exists
        for existing_route in from_province.trade_routes:
            if (existing_route.to_province == to_province.name and
                existing_route.resource == resource_name):
                # Update existing route volume
                existing_route.volume = min(existing_route.volume + volume * 0.5, volume * 2)
                return

        # Create new trade route
        new_route = TradeRoute(
            from_province=from_province.name,
            to_province=to_province.name,
            resource=resource_name,
            volume=volume,
            efficiency=0.7 + random.uniform(-0.1, 0.2),  # 60-90% efficiency
            established_turn=self.turn
        )

        from_province.trade_routes.append(new_route)
        logging.info(f"New trade route established: {from_province.name} -> {to_province.name} ({resource_name})")

    def _update_trade_competition(self):
        """Update trade route efficiency based on competition and market conditions"""
        # Group trade routes by resource and destination
        trade_competition = {}  # (resource, to_province) -> [trade_routes]

        for province in self.map_manager.provinces:
            for route in province.trade_routes:
                key = (route.resource, route.to_province)
                if key not in trade_competition:
                    trade_competition[key] = []
                trade_competition[key].append(route)

        # Update efficiency based on competition
        for (resource, destination), routes in trade_competition.items():
            if len(routes) > 1:  # Competition exists
                # Sort routes by efficiency
                routes.sort(key=lambda r: r.efficiency, reverse=True)

                # Best route gets bonus, others get penalty
                for i, route in enumerate(routes):
                    if i == 0:  # Best route
                        route.efficiency = min(0.95, route.efficiency + 0.05)
                    else:  # Competing routes
                        competition_penalty = 0.02 * i
                        route.efficiency = max(0.3, route.efficiency - competition_penalty)

                        # Very inefficient routes may be abandoned
                        if route.efficiency < 0.4 and random.random() < 0.1:
                            # Find and remove the route
                            for province in self.map_manager.provinces:
                                if route in province.trade_routes:
                                    province.trade_routes.remove(route)
                                    logging.info(f"Trade route abandoned: {route.from_province} -> {route.to_province}")
                                    break

    def establish_trade_agreement(self, country1_name, country2_name, resource_name, terms):
        """Establish a formal trade agreement between two countries"""
        country1 = None
        country2 = None

        for country in self.map_manager.countries:
            if country["name"] == country1_name:
                country1 = country
            elif country["name"] == country2_name:
                country2 = country

        if not country1 or not country2:
            return {"success": False, "error": "One or both countries not found"}

        # Check if countries have the required resources
        country1_provinces = [p for p in self.map_manager.provinces if p.owner == country1_name]
        country2_provinces = [p for p in self.map_manager.provinces if p.owner == country2_name]

        country1_has_resource = any(resource_name in p.resources and p.resources[resource_name] > 1.0
                                  for p in country1_provinces)
        country2_has_resource = any(resource_name in p.resources and p.resources[resource_name] > 1.0
                                  for p in country2_provinces)

        if not (country1_has_resource or country2_has_resource):
            return {"success": False, "error": f"Neither country has sufficient {resource_name}"}

        # Create trade agreement
        agreement = {
            "id": f"trade_{country1_name}_{country2_name}_{resource_name}_{self.turn}",
            "country1": country1_name,
            "country2": country2_name,
            "resource": resource_name,
            "terms": terms,
            "established_turn": self.turn,
            "status": "active",
            "trade_bonus": terms.get("trade_bonus", 0.1)  # 10% efficiency bonus
        }

        # Add to both countries' trade agreements
        if "trade_agreements" not in country1:
            country1["trade_agreements"] = []
        if "trade_agreements" not in country2:
            country2["trade_agreements"] = []

        country1["trade_agreements"].append(agreement)
        country2["trade_agreements"].append(agreement)

        # Improve relations
        country1.setdefault("relations", {})[country2["tag"]] = country1.get("relations", {}).get(country2["tag"], 0) + 10
        country2.setdefault("relations", {})[country1["tag"]] = country2.get("relations", {}).get(country1["tag"], 0) + 10

        # Create or enhance trade routes
        self._enhance_trade_routes_for_agreement(country1_provinces, country2_provinces, resource_name, agreement)

        return {"success": True, "agreement": agreement}

    def _enhance_trade_routes_for_agreement(self, country1_provinces, country2_provinces, resource_name, agreement):
        """Enhance trade routes based on trade agreement"""
        trade_bonus = agreement["trade_bonus"]

        # Find best provinces for trade
        best_producer = None
        best_consumer = None

        for province in country1_provinces:
            if resource_name in province.resources and province.resources[resource_name] > 1.0:
                if not best_producer or province.resources[resource_name] > best_producer.resources[resource_name]:
                    best_producer = province

        for province in country2_provinces:
            consumption = self._calculate_local_consumption(province, resource_name)
            if consumption > 0.5:
                if not best_consumer or consumption > self._calculate_local_consumption(best_consumer, resource_name):
                    best_consumer = province

        # Create enhanced trade route
        if best_producer and best_consumer:
            enhanced_route = TradeRoute(
                from_province=best_producer.name,
                to_province=best_consumer.name,
                resource=resource_name,
                volume=min(best_producer.resources[resource_name] * 0.5, 2.0),
                efficiency=0.8 + trade_bonus,  # Enhanced efficiency
                established_turn=self.turn
            )

            best_producer.trade_routes.append(enhanced_route)
            logging.info(f"Enhanced trade route created for agreement: {best_producer.name} -> {best_consumer.name}")

    def manipulate_market(self, country_name, resource_name, action, investment):
        """Allow countries to manipulate markets through investment"""
        country = None
        for c in self.map_manager.countries:
            if c["name"] == country_name:
                country = c
                break

        if not country:
            return {"success": False, "error": "Country not found"}

        if country["treasury"] < investment:
            return {"success": False, "error": "Insufficient funds"}

        resource = self.resource_manager.global_resources.get(resource_name)
        if not resource:
            return {"success": False, "error": "Resource not found"}

        # Deduct investment
        country["treasury"] -= investment

        # Apply market manipulation based on action
        if action == "corner_market":
            # Attempt to corner the market by buying up supply
            supply_reduction = investment / (resource.current_price * 100)
            resource.supply = max(0.1, resource.supply - supply_reduction)
            resource.demand += supply_reduction * 0.5

            return {
                "success": True,
                "message": f"Market cornering attempt for {resource_name}",
                "effect": f"Supply reduced by {supply_reduction:.2f}, demand increased"
            }

        elif action == "dump_goods":
            # Flood market with goods to drive down prices
            supply_increase = investment / (resource.current_price * 50)
            resource.supply += supply_increase
            resource.demand = max(0.1, resource.demand - supply_increase * 0.3)

            return {
                "success": True,
                "message": f"Market dumping for {resource_name}",
                "effect": f"Supply increased by {supply_increase:.2f}, demand reduced"
            }

        elif action == "create_demand":
            # Artificial demand creation through marketing/subsidies
            demand_increase = investment / (resource.current_price * 80)
            resource.demand += demand_increase

            return {
                "success": True,
                "message": f"Demand creation for {resource_name}",
                "effect": f"Demand increased by {demand_increase:.2f}"
            }

        else:
            return {"success": False, "error": "Unknown market action"}

game = GameManager()

@app.get("/provinces")
def get_provinces():
    """Get all provinces with detailed information"""
    logging.debug("/provinces endpoint called")
    return [p.model_dump() for p in game.map_manager.provinces]

@app.get("/countries")
def get_countries():
    """Get all countries with enhanced game data"""
    logging.debug("/countries endpoint called")
    return game.map_manager.countries

@app.get("/province/{province_name}")
def get_province_details(province_name: str):
    """Get detailed information about a specific province"""
    for province in game.map_manager.provinces:
        if province.name == province_name:
            return province.model_dump()
    return {"error": "Province not found"}

@app.get("/country/{country_name}")
def get_country_details(country_name: str):
    """Get detailed information about a specific country"""
    for country in game.map_manager.countries:
        if country["name"] == country_name:
            return country
    return {"error": "Country not found"}

@app.get("/resources")
def get_global_resources():
    """Get global resource prices and market data"""
    return {name: res.model_dump() for name, res in game.resource_manager.global_resources.items()}

@app.get("/technologies")
def get_technologies():
    """Get all available technologies"""
    return {name: tech.model_dump() for name, tech in game.technologies.items()}

@app.post("/research")
def start_research(data: dict = Body(...)):
    """Start researching a technology"""
    country_name = data.get("country")
    category = data.get("category")
    tech_name = data.get("technology")

    # Find the country
    for country in game.map_manager.countries:
        if country["name"] == country_name:
            # Check if technology exists and prerequisites are met
            if tech_name in game.technologies:
                tech = game.technologies[tech_name]

                # Check prerequisites
                missing_prereqs = [prereq for prereq in tech.prerequisites
                                 if prereq not in country["technologies"]]

                if missing_prereqs:
                    return {"success": False, "error": f"Missing prerequisites: {missing_prereqs}"}

                # Start research
                country["current_research"][category] = tech_name
                return {"success": True, "message": f"Started researching {tech_name}"}

            return {"success": False, "error": "Technology not found"}

    return {"success": False, "error": "Country not found"}

@app.get("/military/{country_name}")
def get_military_units(country_name: str):
    """Get military units for a specific country"""
    for country in game.map_manager.countries:
        if country["name"] == country_name:
            return {
                "units": country.get("military_units", []),
                "military_tradition": country.get("military_tradition", 0),
                "army_size": country.get("army_size", 0),
                "navy_size": country.get("navy_size", 0),
                "military_expenses": country.get("military_expenses", 0)
            }
    return {"error": "Country not found"}



@app.get("/military/unit_types")
def get_unit_types():
    """Get available military unit types and their stats"""
    return game.military_system.unit_types

@app.post("/military/recruit")
def recruit_military_unit(data: dict = Body(...)):
    """Recruit a new military unit"""
    logging.debug(f"/military/recruit endpoint called with data: {data}")
    country_name = data.get("country")
    unit_type = data.get("unit_type")
    province_name = data.get("province")
    size = data.get("size", 1000)

    if not all([country_name, unit_type, province_name]):
        return {"success": False, "error": "Missing required parameters"}

    # Find the country
    country = None
    for c in game.map_manager.countries:
        if c["name"] == country_name:
            country = c
            break

    if not country:
        return {"success": False, "error": "Country not found"}

    # Try to recruit the unit
    success = game.military_system.create_unit(country, unit_type, province_name, size)

    if success:
        logging.info(f"Successfully recruited {unit_type} for {country_name}")
        return {"success": True, "message": f"Recruited {unit_type} unit"}
    else:
        return {"success": False, "error": "Failed to recruit unit - insufficient funds or invalid unit type"}

@app.post("/military/declare_war")
def declare_war(data: dict = Body(...)):
    """Declare war between two countries"""
    logging.debug(f"/military/declare_war endpoint called with data: {data}")
    aggressor_name = data.get("aggressor")
    target_name = data.get("target")
    casus_belli = data.get("casus_belli", "territorial_expansion")

    if not all([aggressor_name, target_name]):
        return {"success": False, "error": "Missing required parameters"}

    # Find both countries
    aggressor_country = None
    target_country = None

    for country in game.map_manager.countries:
        if country["name"] == aggressor_name:
            aggressor_country = country
        elif country["name"] == target_name:
            target_country = country

    if not aggressor_country:
        return {"success": False, "error": "Aggressor country not found"}
    if not target_country:
        return {"success": False, "error": "Target country not found"}

    # Declare war
    war_data = game.military_system.declare_war(aggressor_country, target_country, casus_belli)

    logging.info(f"War declared between {aggressor_name} and {target_name}")
    return {"success": True, "war_data": war_data}

@app.post("/military/conduct_battle")
def conduct_battle(data: dict = Body(...)):
    """Conduct a battle between two countries"""
    logging.debug(f"/military/conduct_battle endpoint called with data: {data}")
    attacker_name = data.get("attacker")
    defender_name = data.get("defender")
    attacking_province = data.get("attacking_province")
    defending_province = data.get("defending_province")

    if not all([attacker_name, defender_name, attacking_province, defending_province]):
        return {"success": False, "error": "Missing required parameters"}

    # Find both countries
    attacker_country = None
    defender_country = None

    for country in game.map_manager.countries:
        if country["name"] == attacker_name:
            attacker_country = country
        elif country["name"] == defender_name:
            defender_country = country

    if not attacker_country:
        return {"success": False, "error": "Attacker country not found"}
    if not defender_country:
        return {"success": False, "error": "Defender country not found"}

    # Conduct battle
    battle_result = game.military_system.conduct_battle(
        attacker_country, defender_country, attacking_province, defending_province
    )

    logging.info(f"Battle conducted: {battle_result}")
    return battle_result

@app.post("/military/siege_province")
def siege_province(data: dict = Body(...)):
    """Attempt to siege and capture a province"""
    logging.debug(f"/military/siege_province endpoint called with data: {data}")
    attacker_name = data.get("attacker")
    target_province = data.get("target_province")

    if not all([attacker_name, target_province]):
        return {"success": False, "error": "Missing required parameters"}

    # Find the attacker country
    attacker_country = None
    for country in game.map_manager.countries:
        if country["name"] == attacker_name:
            attacker_country = country
            break

    if not attacker_country:
        return {"success": False, "error": "Attacker country not found"}

    # Attempt siege
    siege_result = game.military_system.siege_province(
        attacker_country, target_province, game.map_manager.provinces
    )

    logging.info(f"Siege attempt: {siege_result}")
    return siege_result

@app.get("/military/wars")
def get_wars(country: str = Query(None)):
    """Get active wars for a country or all wars"""
    if country:
        # Get wars for specific country
        for c in game.map_manager.countries:
            if c["name"] == country:
                return c.get("wars", [])
        return {"error": "Country not found"}
    else:
        # Get all active wars
        all_wars = []
        for country in game.map_manager.countries:
            country_wars = country.get("wars", [])
            for war in country_wars:
                if war not in all_wars:  # Avoid duplicates
                    all_wars.append(war)
        return all_wars

@app.get("/economy/markets")
def get_market_data():
    """Get current market data for all resources"""
    market_data = {}
    for resource_name, resource in game.resource_manager.global_resources.items():
        market_data[resource_name] = {
            "name": resource_name,
            "current_price": resource.current_price,
            "base_value": resource.base_value,
            "supply": resource.supply,
            "demand": resource.demand,
            "price_change": ((resource.current_price - resource.base_value) / resource.base_value) * 100
        }
    return market_data

@app.post("/economy/trade_agreement")
def establish_trade_agreement(data: dict = Body(...)):
    """Establish a trade agreement between two countries"""
    logging.debug(f"/economy/trade_agreement endpoint called with data: {data}")
    country1 = data.get("country1")
    country2 = data.get("country2")
    resource = data.get("resource")
    terms = data.get("terms", {"trade_bonus": 0.1})

    if not all([country1, country2, resource]):
        return {"success": False, "error": "Missing required parameters"}

    result = game.establish_trade_agreement(country1, country2, resource, terms)
    logging.info(f"Trade agreement result: {result}")
    return result

@app.post("/economy/market_manipulation")
def manipulate_market(data: dict = Body(...)):
    """Manipulate market through economic actions"""
    logging.debug(f"/economy/market_manipulation endpoint called with data: {data}")
    country = data.get("country")
    resource = data.get("resource")
    action = data.get("action")  # corner_market, dump_goods, create_demand
    investment = data.get("investment", 100)

    if not all([country, resource, action]):
        return {"success": False, "error": "Missing required parameters"}

    result = game.manipulate_market(country, resource, action, investment)
    logging.info(f"Market manipulation result: {result}")
    return result

@app.get("/economy/trade_opportunities")
def get_trade_opportunities(country: str = Query(...)):
    """Get available trade opportunities for a country"""
    # Find country's provinces
    country_provinces = [p for p in game.map_manager.provinces if p.owner == country]

    opportunities = []

    # Find resources with surplus
    for province in country_provinces:
        for resource_name, amount in province.resources.items():
            local_consumption = game._calculate_local_consumption(province, resource_name)
            surplus = amount - local_consumption

            if surplus > 0.5:  # Significant surplus
                # Find potential buyers
                for other_province in game.map_manager.provinces:
                    if other_province.owner != country:
                        other_consumption = game._calculate_local_consumption(other_province, resource_name)
                        other_production = other_province.resources.get(resource_name, 0)
                        deficit = other_consumption - other_production

                        if deficit > 0.3:  # Significant deficit
                            resource_data = game.resource_manager.global_resources.get(resource_name)
                            potential_profit = surplus * resource_data.current_price * 0.3 if resource_data else 0

                            opportunities.append({
                                "resource": resource_name,
                                "from_province": province.name,
                                "to_province": other_province.name,
                                "to_country": other_province.owner,
                                "surplus": surplus,
                                "deficit": deficit,
                                "potential_profit": potential_profit,
                                "current_price": resource_data.current_price if resource_data else 0
                            })

    # Sort by potential profit
    opportunities.sort(key=lambda x: x["potential_profit"], reverse=True)

    return opportunities[:10]  # Return top 10 opportunities

@app.get("/economy/country_trade")
def get_country_trade_data(country: str = Query(...)):
    """Get detailed trade data for a country"""
    # Find the country
    country_data = None
    for c in game.map_manager.countries:
        if c["name"] == country:
            country_data = c
            break

    if not country_data:
        return {"error": "Country not found"}

    # Get country's provinces
    country_provinces = [p for p in game.map_manager.provinces if p.owner == country]

    # Collect trade route data
    exports = []
    imports = []

    for province in country_provinces:
        for route in province.trade_routes:
            if route.from_province == province.name:
                # This is an export
                resource_data = game.resource_manager.global_resources.get(route.resource)
                export_value = route.volume * resource_data.current_price * route.efficiency if resource_data else 0

                exports.append({
                    "resource": route.resource,
                    "from_province": route.from_province,
                    "to_province": route.to_province,
                    "volume": route.volume,
                    "efficiency": route.efficiency,
                    "value": export_value
                })

    # Find imports (routes ending in our provinces)
    for province in game.map_manager.provinces:
        for route in province.trade_routes:
            if route.to_province in [p.name for p in country_provinces]:
                # This is an import
                resource_data = game.resource_manager.global_resources.get(route.resource)
                import_value = route.volume * resource_data.current_price * route.efficiency if resource_data else 0

                imports.append({
                    "resource": route.resource,
                    "from_province": route.from_province,
                    "to_province": route.to_province,
                    "volume": route.volume,
                    "efficiency": route.efficiency,
                    "value": import_value
                })

    total_export_value = sum(e["value"] for e in exports)
    total_import_value = sum(i["value"] for i in imports)
    trade_balance = total_export_value - total_import_value

    return {
        "country": country,
        "exports": exports,
        "imports": imports,
        "total_export_value": total_export_value,
        "total_import_value": total_import_value,
        "trade_balance": trade_balance,
        "trade_agreements": country_data.get("trade_agreements", [])
    }

@app.post("/turn")
def advance_turn():
    """Advance the game by one turn"""
    logging.debug("/turn endpoint called")
    result = game.advance_turn()
    logging.debug(f"Turn advanced to {result['turn']}")
    return result

@app.get("/event")
def get_event(country: str = Query(None)):
    logging.debug(f"/event endpoint called for country: {country}")
    # Return the first active event for the country
    active_events = game.event_system.get_active_events(country)
    if active_events:
        return active_events[0]
    return None

@app.get("/events/active")
def get_active_events(country: str = Query(...)):
    """Get all active events for a country"""
    logging.debug(f"/events/active endpoint called for country: {country}")
    return game.event_system.get_active_events(country)

@app.post("/event/choose")
def make_event_choice(data: dict = Body(...)):
    """Handle player's choice for an event"""
    logging.debug(f"/event/choose endpoint called with data: {data}")
    country = data.get("country")
    event_id = data.get("event_id")
    choice_id = data.get("choice_id")

    if not all([country, event_id, choice_id]):
        return {"success": False, "error": "Missing required parameters"}

    result = game.event_system.apply_event_choice(country, event_id, choice_id, game.map_manager.countries)
    logging.debug(f"Event choice result: {result}")
    return result

@app.get("/events/history")
def get_event_history(country: str = Query(...)):
    """Get event history for a country"""
    logging.debug(f"/events/history endpoint called for country: {country}")
    return game.event_system.event_history.get(country, [])

@app.get("/diplomacy")
def get_diplomacy(country: str = Query(None)):
    logging.debug(f"/diplomacy endpoint called for country: {country}")
    return game.diplomacy_system.get_actions(country)

@app.post("/event/apply")
def apply_event_effect(data: dict = Body(...)):
    """Legacy endpoint for backward compatibility"""
    logging.debug(f"/event/apply endpoint called with data: {data}")
    country = data.get("country")
    effect = data.get("effect")
    updated = game.event_system.apply_event(game.map_manager.countries, country, effect)
    if updated:
        logging.debug(f"Event applied to country: {updated}")
        return {"success": True, "country": updated}
    logging.debug("Event apply failed: Country not found")
    return {"success": False, "error": "Country not found"}

@app.post("/diplomacy/perform")
def perform_diplomacy_action(data: dict = Body(...)):
    logging.debug(f"/diplomacy/perform endpoint called with data: {data}")
    country = data.get("country")
    action = data.get("action")
    target = data.get("target")
    result = game.diplomacy_system.perform_action(game.map_manager.countries, country, action, target)
    logging.debug(f"Diplomacy action result: {result}")
    return result

@app.get("/ai/activity")
def get_ai_activity():
    """Get recent AI activity and decisions"""
    ai_activity = []

    # Get recent AI decisions from the diplomacy system
    if hasattr(game.diplomacy_system, 'ai_system'):
        for country_name, decisions in game.diplomacy_system.ai_system.decision_history.items():
            # Get the last few decisions for each country
            recent_decisions = decisions[-3:] if len(decisions) > 3 else decisions

            for decision in recent_decisions:
                ai_activity.append({
                    "country": decision.country,
                    "action": decision.action,
                    "target": decision.target,
                    "reasoning": decision.reasoning,
                    "decision_type": decision.decision_type,
                    "priority": decision.priority
                })

    # Sort by priority (most important first)
    ai_activity.sort(key=lambda x: x["priority"], reverse=True)

    return ai_activity[:10]  # Return top 10 recent activities

@app.get("/ai/threats")
def get_threat_assessment(country: str = Query(...)):
    """Get AI threat assessment for a specific country"""
    if hasattr(game.diplomacy_system, 'ai_system'):
        ai_system = game.diplomacy_system.ai_system

        # Find the country
        target_country = None
        for c in game.map_manager.countries:
            if c["name"] == country:
                target_country = c
                break

        if target_country:
            situation = ai_system._assess_situation(target_country, game.map_manager.countries,
                                                  game.map_manager.provinces, game)
            return {
                "country": country,
                "threats": situation["threats"],
                "opportunities": situation["opportunities"],
                "economic_strength": situation["economic_strength"],
                "military_strength": situation["military_strength"]
            }

    return {"error": "Country not found or AI system not available"}

@app.get("/diplomacy/alliances")
def get_alliances():
    """Get all active alliances"""
    return list(game.diplomacy_system.alliances.values())

@app.get("/diplomacy/treaties")
def get_treaties():
    """Get all active treaties"""
    return list(game.diplomacy_system.treaties.values())

@app.get("/diplomacy/incidents")
def get_diplomatic_incidents():
    """Get recent diplomatic incidents"""
    return list(game.diplomacy_system.diplomatic_incidents.values())

@app.get("/diplomacy/relations")
def get_diplomatic_relations(country: str = Query(...)):
    """Get detailed diplomatic relations for a country"""
    target_country = None
    for c in game.map_manager.countries:
        if c["name"] == country:
            target_country = c
            break

    if not target_country:
        return {"error": "Country not found"}

    relations_data = {
        "country": country,
        "relations": target_country.get("relations", {}),
        "alliances": [],
        "embassies": target_country.get("embassies", []),
        "trade_embargoes": target_country.get("trade_embargoes", []),
        "cultural_exchanges": target_country.get("cultural_exchanges", []),
        "diplomatic_reputation": target_country.get("diplomatic_reputation", 0)
    }

    # Find alliances this country is part of
    for alliance in game.diplomacy_system.alliances.values():
        if country in alliance.members:
            relations_data["alliances"].append({
                "id": alliance.id,
                "name": alliance.name,
                "type": alliance.alliance_type,
                "members": alliance.members,
                "leader": alliance.leader
            })

    return relations_data

@app.post("/diplomacy/alliance")
def propose_alliance(data: dict = Body(...)):
    """Propose an alliance between two countries"""
    logging.debug(f"/diplomacy/alliance endpoint called with data: {data}")
    initiator = data.get("initiator")
    target = data.get("target")
    alliance_type = data.get("alliance_type", "defensive")
    terms = data.get("terms", {})

    if not all([initiator, target]):
        return {"success": False, "error": "Missing required parameters"}

    result = game.diplomacy_system.propose_alliance(initiator, target, alliance_type, terms, game.map_manager.countries)
    logging.info(f"Alliance proposal result: {result}")
    return result

@app.get("/diplomacy/espionage")
def get_espionage_networks(country: str = Query(...)):
    """Get espionage networks for a country"""
    networks = game.diplomacy_system.espionage_networks.get(country, {})

    espionage_data = {
        "country": country,
        "networks": []
    }

    for target, strength in networks.items():
        espionage_data["networks"].append({
            "target": target,
            "strength": strength,
            "effectiveness": min(100, strength * 100)
        })

    return espionage_data

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
