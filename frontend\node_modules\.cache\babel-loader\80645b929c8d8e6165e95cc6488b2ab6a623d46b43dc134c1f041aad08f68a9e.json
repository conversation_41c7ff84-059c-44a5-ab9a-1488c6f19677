{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\n\n// Enhanced game constants\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WIN_CONDITIONS = {\n  treasury: 10000,\n  prestige: 100,\n  provinces: 10\n};\nconst LOSE_CONDITIONS = {\n  stability: 10,\n  treasury: -5000,\n  legitimacy: 20\n};\nexport default function App() {\n  _s();\n  // Core game state\n  const [countries, setCountries] = useState([]);\n  const [provinces, setProvinces] = useState([]);\n  const [playerCountry, setPlayerCountry] = useState(null);\n  const [turn, setTurn] = useState(1);\n  const [message, setMessage] = useState(\"\");\n  const [gamePhase, setGamePhase] = useState(\"loading\"); // loading, country_selection, playing, victory, defeat\n\n  // Enhanced game data\n  const [globalResources, setGlobalResources] = useState({});\n  const [technologies, setTechnologies] = useState({});\n  const [militaryData, setMilitaryData] = useState({});\n  const [unitTypes, setUnitTypes] = useState({});\n  const [selectedProvince, setSelectedProvince] = useState(null);\n  const [activeTab, setActiveTab] = useState(\"overview\"); // overview, population, economy, military, diplomacy, technology\n\n  // UI state\n  const [actionTaken, setActionTaken] = useState(false);\n  const [event, setEvent] = useState(null);\n  const [notifications, setNotifications] = useState([]);\n\n  // Load initial game data\n  useEffect(() => {\n    const loadGameData = async () => {\n      try {\n        setGamePhase(\"loading\");\n\n        // Load all game data in parallel\n        const [countriesRes, provincesRes, resourcesRes, techRes, unitTypesRes] = await Promise.all([axios.get(\"http://localhost:8000/countries\"), axios.get(\"http://localhost:8000/provinces\"), axios.get(\"http://localhost:8000/resources\"), axios.get(\"http://localhost:8000/technologies\"), axios.get(\"http://localhost:8000/military/unit_types\")]);\n        console.log(\"Game data loaded:\", {\n          countries: countriesRes.data.length,\n          provinces: provincesRes.data.length,\n          resources: Object.keys(resourcesRes.data).length,\n          technologies: Object.keys(techRes.data).length,\n          unitTypes: Object.keys(unitTypesRes.data).length\n        });\n        setCountries(countriesRes.data);\n        setProvinces(provincesRes.data);\n        setGlobalResources(resourcesRes.data);\n        setTechnologies(techRes.data);\n        setUnitTypes(unitTypesRes.data);\n        setGamePhase(\"country_selection\");\n      } catch (error) {\n        console.error(\"Failed to load game data:\", error);\n        setMessage(\"Failed to load game data. Please refresh the page.\");\n      }\n    };\n    loadGameData();\n  }, []);\n\n  // Fetch events when turn changes\n  useEffect(() => {\n    if (!playerCountry || gamePhase !== \"playing\") return;\n    const fetchTurnEvents = async () => {\n      try {\n        const eventRes = await axios.get(`http://localhost:8000/event?country=${encodeURIComponent(playerCountry.name)}`);\n        setEvent(eventRes.data);\n      } catch (error) {\n        console.error(\"Failed to fetch events:\", error);\n      }\n    };\n    fetchTurnEvents();\n  }, [turn, playerCountry, gamePhase]);\n\n  // Game phase handlers\n  const selectCountry = async country => {\n    setPlayerCountry(country);\n    setGamePhase(\"playing\");\n    setActiveTab(\"overview\");\n    addNotification(`Welcome, ${country.ruler_name} of ${country.name}!`, \"success\");\n\n    // Load military data for the selected country\n    try {\n      const militaryRes = await axios.get(`http://localhost:8000/military/${country.name}`);\n      setMilitaryData(militaryRes.data);\n    } catch (error) {\n      console.error(\"Failed to load military data:\", error);\n    }\n  };\n  const addNotification = (text, type = \"info\") => {\n    const notification = {\n      id: Date.now(),\n      text,\n      type,\n      timestamp: new Date().toLocaleTimeString()\n    };\n    setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications\n  };\n\n  // Enhanced turn advancement\n  const advanceTurn = async () => {\n    try {\n      setMessage(\"Processing turn...\");\n      const response = await axios.post(\"http://localhost:8000/turn\");\n\n      // Update all game state\n      setTurn(response.data.turn);\n      setCountries(response.data.countries);\n      setProvinces(response.data.provinces);\n      setGlobalResources(response.data.global_resources);\n\n      // Update player country reference\n      const updatedPlayerCountry = response.data.countries.find(c => c.name === playerCountry.name);\n      if (updatedPlayerCountry) {\n        setPlayerCountry(updatedPlayerCountry);\n\n        // Refresh military data\n        try {\n          const militaryRes = await axios.get(`http://localhost:8000/military/${updatedPlayerCountry.name}`);\n          setMilitaryData(militaryRes.data);\n        } catch (error) {\n          console.error(\"Failed to refresh military data:\", error);\n        }\n      }\n      setActionTaken(false);\n      setMessage(\"\");\n      addNotification(`Turn ${response.data.turn} begins`, \"info\");\n\n      // Check win/lose conditions\n      checkGameEnd(updatedPlayerCountry);\n    } catch (error) {\n      console.error(\"Failed to advance turn:\", error);\n      setMessage(\"Failed to advance turn. Please try again.\");\n    }\n  };\n  const checkGameEnd = country => {\n    if (!country) return;\n\n    // Check victory conditions\n    if (country.treasury >= WIN_CONDITIONS.treasury || country.prestige >= WIN_CONDITIONS.prestige || country.provinces.length >= WIN_CONDITIONS.provinces) {\n      setGamePhase(\"victory\");\n      return;\n    }\n\n    // Check defeat conditions\n    if (country.stability <= LOSE_CONDITIONS.stability || country.treasury <= LOSE_CONDITIONS.treasury || country.legitimacy <= LOSE_CONDITIONS.legitimacy) {\n      setGamePhase(\"defeat\");\n      return;\n    }\n  };\n\n  // Research technology\n  const startResearch = async (category, techName) => {\n    try {\n      const response = await axios.post(\"http://localhost:8000/research\", {\n        country: playerCountry.name,\n        category,\n        technology: techName\n      });\n      if (response.data.success) {\n        addNotification(response.data.message, \"success\");\n        // Refresh country data\n        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);\n        setPlayerCountry(countryRes.data);\n      } else {\n        addNotification(response.data.error, \"error\");\n      }\n    } catch (error) {\n      console.error(\"Research failed:\", error);\n      addNotification(\"Failed to start research\", \"error\");\n    }\n  };\n\n  // Tab content renderer\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'overview':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83C\\uDFDB\\uFE0F Nation Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '20px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#2d2d44',\n                padding: '15px',\n                borderRadius: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDCB0 Economy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Monthly Income:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.monthly_income.toFixed(1), \" gold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Monthly Expenses:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.monthly_expenses.toFixed(1), \" gold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Net Income:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 20\n                }, this), \" \", (playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1), \" gold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Inflation:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 20\n                }, this), \" \", (playerCountry.inflation * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#2d2d44',\n                padding: '15px',\n                borderRadius: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u2694\\uFE0F Military\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Army Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.army_size.toLocaleString(), \" troops\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Navy Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.navy_size, \" ships\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Military Tradition:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.military_tradition.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"War Exhaustion:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.war_exhaustion.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\uD83C\\uDFDB\\uFE0F Provinces (\", playerCountry.provinces.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '10px'\n              },\n              children: provinces.filter(p => p.owner === playerCountry.name).map(province => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '10px',\n                  borderRadius: '5px',\n                  cursor: 'pointer',\n                  border: (selectedProvince === null || selectedProvince === void 0 ? void 0 : selectedProvince.name) === province.name ? '2px solid #4a9eff' : '1px solid #444'\n                },\n                onClick: () => setSelectedProvince(province),\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: province.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Dev: \", province.development.toFixed(1), \" | Pop: \", province.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this)]\n              }, province.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), event && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCF0 Current Event\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#1a1a2e',\n                padding: '15px',\n                borderRadius: '5px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: event.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: event.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#4a9eff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Effect:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 51\n                }, this), \" \", event.effect]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  // Apply event logic here\n                  addNotification(`Applied: ${event.effect}`, \"success\");\n                  setEvent(null);\n                },\n                style: {\n                  background: '#4a9eff',\n                  color: '#fff',\n                  border: 'none',\n                  padding: '8px 15px',\n                  borderRadius: '5px',\n                  cursor: 'pointer'\n                },\n                children: \"Accept\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this);\n      case 'population':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDC65 Population Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCCA Population Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '15px'\n              },\n              children: provinces.filter(p => p.owner === playerCountry.name).map(province => {\n                const totalPop = province.population_groups.reduce((sum, pop) => sum + pop.size, 0);\n                const avgHappiness = province.population_groups.reduce((sum, pop) => sum + pop.happiness, 0) / province.population_groups.length;\n                const avgMilitancy = province.population_groups.reduce((sum, pop) => sum + pop.militancy, 0) / province.population_groups.length;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: '#1a1a2e',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    border: (selectedProvince === null || selectedProvince === void 0 ? void 0 : selectedProvince.name) === province.name ? '2px solid #4a9eff' : '1px solid #444'\n                  },\n                  onClick: () => setSelectedProvince(province),\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    style: {\n                      margin: '0 0 8px 0'\n                    },\n                    children: province.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '2px 0',\n                      fontSize: '0.9rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Population:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 25\n                    }, this), \" \", totalPop.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '2px 0',\n                      fontSize: '0.9rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Happiness:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 25\n                    }, this), \" \", avgHappiness.toFixed(1), \"/10\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '2px 0',\n                      fontSize: '0.9rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Unrest:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 25\n                    }, this), \" \", province.unrest.toFixed(1)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: avgHappiness > 6 ? '#51cf66' : avgHappiness > 4 ? '#ffd43b' : '#ff6b6b',\n                      height: '4px',\n                      borderRadius: '2px',\n                      marginTop: '8px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this)]\n                }, province.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), selectedProvince && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\uD83C\\uDFD8\\uFE0F \", selectedProvince.name, \" - Population Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '15px'\n              },\n              children: selectedProvince.population_groups.map((popGroup, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  border: '1px solid #444'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0',\n                    textTransform: 'capitalize'\n                  },\n                  children: popGroup.social_class.replace('_', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.size.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Culture:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.culture]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Religion:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.religion]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Profession:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.profession]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Wealth:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.wealth.toFixed(1)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Happiness:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 360,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b'\n                        },\n                        children: [popGroup.happiness.toFixed(1), \"/10\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b',\n                          height: '100%',\n                          width: `${popGroup.happiness / 10 * 100}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 371,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Militancy:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66'\n                        },\n                        children: [popGroup.militancy.toFixed(1), \"/10\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66',\n                          height: '100%',\n                          width: `${popGroup.militancy / 10 * 100}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Consciousness:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 404,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#4a9eff'\n                        },\n                        children: [popGroup.consciousness.toFixed(1), \"/10\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#4a9eff',\n                          height: '100%',\n                          width: `${popGroup.consciousness / 10 * 100}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83D\\uDC51 Population Policies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Implemented education reforms\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#4a9eff',\n                    color: '#fff',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83D\\uDCDA Education Reform\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 42\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Increase consciousness\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Promoted cultural integration\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#51cf66',\n                    color: '#fff',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83E\\uDD1D Cultural Integration\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 46\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Reduce cultural tensions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Implemented welfare programs\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#ffd43b',\n                    color: '#000',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83C\\uDFE5 Welfare Programs\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 42\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Increase happiness\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDF0D National Demographics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), (() => {\n              const allPops = provinces.filter(p => p.owner === playerCountry.name).flatMap(p => p.population_groups);\n              const totalPop = allPops.reduce((sum, pop) => sum + pop.size, 0);\n              const classCounts = {};\n              const cultureCounts = {};\n              const religionCounts = {};\n              allPops.forEach(pop => {\n                classCounts[pop.social_class] = (classCounts[pop.social_class] || 0) + pop.size;\n                cultureCounts[pop.culture] = (cultureCounts[pop.culture] || 0) + pop.size;\n                religionCounts[pop.religion] = (religionCounts[pop.religion] || 0) + pop.size;\n              });\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '20px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83D\\uDC51 Social Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this), Object.entries(classCounts).map(([className, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '8px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          textTransform: 'capitalize'\n                        },\n                        children: className.replace('_', ' ')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(count / totalPop * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#4a9eff',\n                          height: '100%',\n                          width: `${count / totalPop * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 530,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 27\n                    }, this)]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83C\\uDFDB\\uFE0F Cultures\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 23\n                  }, this), Object.entries(cultureCounts).map(([culture, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '8px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          textTransform: 'capitalize'\n                        },\n                        children: culture\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(count / totalPop * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 546,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#51cf66',\n                          height: '100%',\n                          width: `${count / totalPop * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 549,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 27\n                    }, this)]\n                  }, culture, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u26EA Religions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 23\n                  }, this), Object.entries(religionCounts).map(([religion, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '8px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          textTransform: 'capitalize'\n                        },\n                        children: religion\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 564,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(count / totalPop * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#ffd43b',\n                          height: '100%',\n                          width: `${count / totalPop * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 27\n                    }, this)]\n                  }, religion, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this);\n      case 'economy':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDCB0 Economic Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCCA Financial Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Treasury:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 22\n                  }, this), \" \", playerCountry.treasury.toLocaleString(), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Monthly Income:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 22\n                  }, this), \" +\", playerCountry.monthly_income.toFixed(1), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Monthly Expenses:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 22\n                  }, this), \" -\", playerCountry.monthly_expenses.toFixed(1), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Net Balance:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 22\n                  }, this), \" \", (playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Trade Efficiency:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 22\n                  }, this), \" \", (playerCountry.trade_efficiency * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Inflation Rate:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 22\n                  }, this), \" \", (playerCountry.inflation * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDF0D Global Resource Prices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '10px'\n              },\n              children: Object.entries(globalResources).map(([name, resource]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '10px',\n                  borderRadius: '5px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: name.replace('_', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#4a9eff'\n                  },\n                  children: [resource.current_price.toFixed(2), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  style: {\n                    opacity: 0.7\n                  },\n                  children: resource.current_price > resource.base_value ? '📈' : '📉'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this)]\n              }, name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDFED Economic Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '15px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  if (!actionTaken) {\n                    addNotification(\"Invested in infrastructure development\", \"success\");\n                    setActionTaken(true);\n                  }\n                },\n                disabled: actionTaken,\n                style: {\n                  background: actionTaken ? '#666' : '#4a9eff',\n                  color: '#fff',\n                  border: 'none',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  cursor: actionTaken ? 'not-allowed' : 'pointer'\n                },\n                children: [\"\\uD83C\\uDFD7\\uFE0F Build Infrastructure\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 43\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"Cost: 200 gold\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  if (!actionTaken) {\n                    addNotification(\"Promoted trade and commerce\", \"success\");\n                    setActionTaken(true);\n                  }\n                },\n                disabled: actionTaken,\n                style: {\n                  background: actionTaken ? '#666' : '#51cf66',\n                  color: '#fff',\n                  border: 'none',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  cursor: actionTaken ? 'not-allowed' : 'pointer'\n                },\n                children: [\"\\uD83C\\uDFEA Promote Trade\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 35\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"Cost: 150 gold\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  if (!actionTaken) {\n                    addNotification(\"Implemented tax reforms\", \"success\");\n                    setActionTaken(true);\n                  }\n                },\n                disabled: actionTaken,\n                style: {\n                  background: actionTaken ? '#666' : '#ffd43b',\n                  color: '#000',\n                  border: 'none',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  cursor: actionTaken ? 'not-allowed' : 'pointer'\n                },\n                children: [\"\\uD83D\\uDCCB Tax Reform\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 32\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"Cost: 100 gold\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this);\n      case 'military':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u2694\\uFE0F Military Command\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDFDB\\uFE0F Military Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '15px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0'\n                  },\n                  children: \"\\uD83E\\uDE96 Total Army\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '1.5rem',\n                    fontWeight: 'bold',\n                    color: '#4a9eff'\n                  },\n                  children: militaryData.units ? militaryData.units.filter(u => !['frigate', 'ship_of_line'].includes(u.unit_type)).reduce((sum, u) => sum + u.size, 0).toLocaleString() : '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"troops\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0'\n                  },\n                  children: \"\\u26F5 Total Navy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '1.5rem',\n                    fontWeight: 'bold',\n                    color: '#51cf66'\n                  },\n                  children: militaryData.units ? militaryData.units.filter(u => ['frigate', 'ship_of_line'].includes(u.unit_type)).length : '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"ships\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0'\n                  },\n                  children: \"\\uD83C\\uDF96\\uFE0F Military Tradition\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '1.5rem',\n                    fontWeight: 'bold',\n                    color: '#ffd43b'\n                  },\n                  children: militaryData.military_tradition ? militaryData.military_tradition.toFixed(1) : '0.0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"/ 100\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0'\n                  },\n                  children: \"\\uD83D\\uDCB0 Military Expenses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '1.5rem',\n                    fontWeight: 'bold',\n                    color: '#ff6b6b'\n                  },\n                  children: militaryData.military_expenses ? militaryData.military_expenses.toFixed(1) : '0.0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"gold/month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83E\\uDE96 Military Units\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 15\n            }, this), militaryData.units && militaryData.units.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                gap: '15px'\n              },\n              children: militaryData.units.map((unit, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  border: '1px solid #444'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0',\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [unit.unit_type === 'infantry' && '🪖', unit.unit_type === 'cavalry' && '🐎', unit.unit_type === 'artillery' && '💣', unit.unit_type === 'frigate' && '⛵', unit.unit_type === 'ship_of_line' && '🚢', /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '8px'\n                    },\n                    children: unit.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 761,\n                      columnNumber: 28\n                    }, this), \" \", unit.unit_type.replace('_', ' ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 762,\n                      columnNumber: 28\n                    }, this), \" \", unit.size.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Location:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 763,\n                      columnNumber: 28\n                    }, this), \" \", unit.location]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Maintenance:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 764,\n                      columnNumber: 28\n                    }, this), \" \", unit.maintenance_cost.toFixed(1), \" gold/month\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Strength:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 768,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: unit.strength > 80 ? '#51cf66' : unit.strength > 50 ? '#ffd43b' : '#ff6b6b'\n                        },\n                        children: [unit.strength.toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 769,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 767,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: unit.strength > 80 ? '#51cf66' : unit.strength > 50 ? '#ffd43b' : '#ff6b6b',\n                          height: '100%',\n                          width: `${unit.strength}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 779,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Morale:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 790,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: unit.morale > 70 ? '#51cf66' : unit.morale > 40 ? '#ffd43b' : '#ff6b6b'\n                        },\n                        children: [unit.morale.toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 791,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 789,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: unit.morale > 70 ? '#51cf66' : unit.morale > 40 ? '#ffd43b' : '#ff6b6b',\n                          height: '100%',\n                          width: `${unit.morale}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 801,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 795,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Experience:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#4a9eff'\n                        },\n                        children: [unit.experience.toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 813,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 811,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#4a9eff',\n                          height: '100%',\n                          width: `${unit.experience}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 823,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 817,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 810,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                textAlign: 'center',\n                color: '#888',\n                padding: '20px'\n              },\n              children: \"No military units. Recruit some units to defend your nation!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDFED Unit Recruitment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '15px'\n              },\n              children: Object.entries(unitTypes).map(([unitType, stats]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  border: '1px solid #444'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0',\n                    textTransform: 'capitalize',\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [unitType === 'infantry' && '🪖', unitType === 'cavalry' && '🐎', unitType === 'artillery' && '💣', unitType === 'frigate' && '⛵', unitType === 'ship_of_line' && '🚢', /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '8px'\n                    },\n                    children: unitType.replace('_', ' ')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    marginBottom: '15px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cost:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 26\n                    }, this), \" \", stats.cost, \" gold\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Maintenance:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 26\n                    }, this), \" \", stats.maintenance, \" gold/month\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Strength:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 26\n                    }, this), \" \", stats.strength]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Speed:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 26\n                    }, this), \" \", stats.speed]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken && playerCountry.provinces.length > 0) {\n                      const capital = playerCountry.provinces[0];\n                      recruitUnit(unitType, capital, 1000);\n                    }\n                  },\n                  disabled: actionTaken || playerCountry.treasury < stats.cost,\n                  style: {\n                    background: actionTaken || playerCountry.treasury < stats.cost ? '#666' : '#4a9eff',\n                    color: '#fff',\n                    border: 'none',\n                    padding: '10px 15px',\n                    borderRadius: '5px',\n                    cursor: actionTaken || playerCountry.treasury < stats.cost ? 'not-allowed' : 'pointer',\n                    width: '100%',\n                    fontSize: '0.9rem'\n                  },\n                  children: playerCountry.treasury < stats.cost ? 'Insufficient Funds' : actionTaken ? 'Action Taken' : `Recruit ${unitType.replace('_', ' ')}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this)]\n              }, unitType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '20px',\n                padding: '15px',\n                background: '#1a1a2e',\n                borderRadius: '5px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u26A1 Military Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Conducted military training exercises\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#51cf66',\n                    color: '#fff',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83C\\uDFAF Military Training\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Improve unit experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Reorganized military structure\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#ffd43b',\n                    color: '#000',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83D\\uDCCB Military Reform\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 39\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Reduce maintenance costs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Fortified border provinces\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#ff6b6b',\n                    color: '#fff',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83C\\uDFF0 Build Fortifications\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 44\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Improve defense\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 962,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 11\n        }, this);\n      case 'technology':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDD2C Technology & Research\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 973,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCDA Research Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '15px'\n              },\n              children: Object.entries(playerCountry.research_points).map(([category, points]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    textTransform: 'capitalize',\n                    margin: '0 0 10px 0'\n                  },\n                  children: [category === 'military' && '⚔️', category === 'economic' && '💰', category === 'social' && '👥', category === 'administrative' && '🏛️', ' ' + category]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Points:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 24\n                  }, this), \" \", points.toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 21\n                }, this), playerCountry.current_research[category] && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Researching:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 989,\n                    columnNumber: 26\n                  }, this), \" \", playerCountry.current_research[category]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 23\n                }, this)]\n              }, category, true, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83E\\uDDEA Available Technologies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                gap: '15px'\n              },\n              children: Object.entries(technologies).map(([name, tech]) => {\n                const isResearched = playerCountry.technologies.includes(name);\n                const canResearch = tech.prerequisites.every(prereq => playerCountry.technologies.includes(prereq));\n                const isCurrentlyResearching = Object.values(playerCountry.current_research).includes(name);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: isResearched ? '#2d5a2d' : '#1a1a2e',\n                    padding: '15px',\n                    borderRadius: '5px',\n                    border: isCurrentlyResearching ? '2px solid #4a9eff' : '1px solid #444',\n                    opacity: canResearch || isResearched ? 1 : 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    style: {\n                      margin: '0 0 10px 0'\n                    },\n                    children: name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      margin: '5px 0'\n                    },\n                    children: tech.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Category:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 26\n                    }, this), \" \", tech.category]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1017,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cost:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 26\n                    }, this), \" \", tech.cost, \" points\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1018,\n                    columnNumber: 23\n                  }, this), tech.prerequisites.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Prerequisites:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1021,\n                      columnNumber: 28\n                    }, this), \" \", tech.prerequisites.join(', ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1021,\n                    columnNumber: 25\n                  }, this), isResearched && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#51cf66',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u2705 Researched\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 25\n                  }, this), !isResearched && canResearch && !isCurrentlyResearching && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startResearch(tech.category, name),\n                    style: {\n                      background: '#4a9eff',\n                      color: '#fff',\n                      border: 'none',\n                      padding: '8px 15px',\n                      borderRadius: '5px',\n                      cursor: 'pointer',\n                      marginTop: '10px'\n                    },\n                    children: \"Start Research\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 25\n                  }, this), isCurrentlyResearching && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#ffd43b',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\uD83D\\uDD2C Researching...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1046,\n                    columnNumber: 25\n                  }, this)]\n                }, name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 972,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDEA7 Coming Soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This feature is under development and will be available in future updates.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1060,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1058,\n          columnNumber: 11\n        }, this);\n    }\n  };\n\n  // Military recruitment\n  const recruitUnit = async (unitType, province, size = 1000) => {\n    try {\n      const response = await axios.post(\"http://localhost:8000/military/recruit\", {\n        country: playerCountry.name,\n        unit_type: unitType,\n        province: province,\n        size: size\n      });\n      if (response.data.success) {\n        addNotification(response.data.message, \"success\");\n\n        // Refresh military and country data\n        const [militaryRes, countryRes] = await Promise.all([axios.get(`http://localhost:8000/military/${playerCountry.name}`), axios.get(`http://localhost:8000/country/${playerCountry.name}`)]);\n        setMilitaryData(militaryRes.data);\n        setPlayerCountry(countryRes.data);\n        setActionTaken(true);\n      } else {\n        addNotification(response.data.error, \"error\");\n      }\n    } catch (error) {\n      console.error(\"Recruitment failed:\", error);\n      addNotification(\"Failed to recruit unit\", \"error\");\n    }\n  };\n\n  // Game phase rendering\n  if (gamePhase === \"loading\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        background: '#1a1a2e'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          color: '#fff'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83C\\uDFF0 Empires & Revolutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading the world of Aeterra...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '20px 0'\n          },\n          children: \"\\u2694\\uFE0F \\uD83C\\uDFDB\\uFE0F \\uD83D\\uDCB0 \\uD83D\\uDD2C \\uD83C\\uDF0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1100,\n      columnNumber: 7\n    }, this);\n  }\n  if (gamePhase === \"country_selection\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#1a1a2e',\n        minHeight: '100vh',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83C\\uDFF0 Choose Your Destiny\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select a nation to lead through the Age of Revolutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n        onSelectCountry: selectCountry,\n        selectedCountry: null,\n        countries: countries\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Available Nations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '20px'\n          },\n          children: countries.slice(0, 5).map(country => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '20px',\n              borderRadius: '10px',\n              cursor: 'pointer',\n              border: '2px solid transparent',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.borderColor = '#4a9eff',\n            onMouseLeave: e => e.target.style.borderColor = 'transparent',\n            onClick: () => selectCountry(country),\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: country.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ruler:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 20\n              }, this), \" \", country.ruler_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Government:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 20\n              }, this), \" \", country.government_type.replace('_', ' ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Treasury:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 20\n              }, this), \" \", country.treasury.toLocaleString(), \" gold\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Provinces:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 20\n              }, this), \" \", country.provinces.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Prestige:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 20\n              }, this), \" \", country.prestige]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1147,\n              columnNumber: 17\n            }, this)]\n          }, country.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1128,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1112,\n      columnNumber: 7\n    }, this);\n  }\n  if (gamePhase === \"victory\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        minHeight: '100vh',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        color: '#fff',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '20px'\n          },\n          children: \"\\uD83C\\uDF89 VICTORY! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"The \", playerCountry.name, \" Triumphant!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '1.5rem',\n            margin: '20px 0'\n          },\n          children: [\"Under the wise rule of \", playerCountry.ruler_name, \", your nation has achieved greatness!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(0,0,0,0.3)',\n            padding: '20px',\n            borderRadius: '10px',\n            margin: '20px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Treasury:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1174,\n              columnNumber: 16\n            }, this), \" \", playerCountry.treasury.toLocaleString(), \" gold\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Prestige:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 16\n            }, this), \" \", playerCountry.prestige]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Provinces Controlled:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1176,\n              columnNumber: 16\n            }, this), \" \", playerCountry.provinces.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Turns Survived:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1177,\n              columnNumber: 16\n            }, this), \" \", turn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            padding: '15px 30px',\n            fontSize: '1.2rem',\n            background: '#4a9eff',\n            color: '#fff',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          },\n          children: \"Play Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1158,\n      columnNumber: 7\n    }, this);\n  }\n  if (gamePhase === \"defeat\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',\n        minHeight: '100vh',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        color: '#fff',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '20px'\n          },\n          children: \"\\uD83D\\uDC80 DEFEAT \\uD83D\\uDC80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"The Fall of \", playerCountry.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '1.5rem',\n            margin: '20px 0'\n          },\n          children: [\"The reign of \", playerCountry.ruler_name, \" has come to an end...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(0,0,0,0.3)',\n            padding: '20px',\n            borderRadius: '10px',\n            margin: '20px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Treasury:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1216,\n              columnNumber: 16\n            }, this), \" \", playerCountry.treasury.toLocaleString(), \" gold\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Stability:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1217,\n              columnNumber: 16\n            }, this), \" \", playerCountry.stability]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Legitimacy:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 16\n            }, this), \" \", playerCountry.legitimacy]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Turns Survived:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1219,\n              columnNumber: 16\n            }, this), \" \", turn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            padding: '15px 30px',\n            fontSize: '1.2rem',\n            background: '#4a9eff',\n            color: '#fff',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          },\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1200,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main game interface - sophisticated tabbed layout\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: '#1a1a2e',\n      minHeight: '100vh',\n      color: '#fff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#2d2d44',\n        padding: '15px 20px',\n        borderBottom: '2px solid #4a9eff',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: 0,\n            fontSize: '1.8rem'\n          },\n          children: [\"\\uD83C\\uDFF0 \", playerCountry.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            opacity: 0.8\n          },\n          children: [playerCountry.ruler_name, \" \\u2022 \", playerCountry.government_type.replace('_', ' ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83D\\uDCB0 \", playerCountry.treasury.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Treasury\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\u2B50 \", playerCountry.prestige]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Prestige\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83C\\uDFDB\\uFE0F \", playerCountry.stability]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Stability\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83D\\uDC51 \", playerCountry.legitimacy]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Legitimacy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83D\\uDCC5 \", turn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Turn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1244,\n      columnNumber: 7\n    }, this), notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '10px 20px'\n      },\n      children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: notification.type === 'error' ? '#ff6b6b' : notification.type === 'success' ? '#51cf66' : '#4a9eff',\n          padding: '8px 15px',\n          margin: '5px 0',\n          borderRadius: '5px',\n          fontSize: '0.9rem',\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: notification.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1298,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: notification.timestamp\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1299,\n          columnNumber: 15\n        }, this)]\n      }, notification.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1285,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1283,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#2d2d44',\n        padding: '0 20px',\n        borderBottom: '1px solid #444'\n      },\n      children: ['overview', 'population', 'economy', 'military', 'diplomacy', 'technology'].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setActiveTab(tab),\n        style: {\n          background: activeTab === tab ? '#4a9eff' : 'transparent',\n          color: '#fff',\n          border: 'none',\n          padding: '15px 25px',\n          margin: '0 5px',\n          cursor: 'pointer',\n          borderRadius: '5px 5px 0 0',\n          textTransform: 'capitalize',\n          fontSize: '1rem'\n        },\n        children: [tab === 'overview' && '🏛️', tab === 'population' && '👥', tab === 'economy' && '💰', tab === 'military' && '⚔️', tab === 'diplomacy' && '🤝', tab === 'technology' && '🔬', ' ' + tab]\n      }, tab, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1312,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1306,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        height: 'calc(100vh - 200px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          background: '#16213e',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(WorldMap, {\n          onSelectCountry: () => {},\n          selectedCountry: playerCountry,\n          countries: countries,\n          onSelectProvince: setSelectedProvince,\n          selectedProvince: selectedProvince\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1342,\n          columnNumber: 11\n        }, this), selectedProvince && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#2d2d44',\n            padding: '15px',\n            margin: '10px 0',\n            borderRadius: '8px',\n            maxHeight: '200px',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: selectedProvince.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Owner:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1360,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.owner]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Development:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.development.toFixed(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1361,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Population:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1362,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1362,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Terrain:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1363,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.terrain]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Unrest:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1364,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.unrest.toFixed(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1364,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1351,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1341,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          background: '#1a1a2e',\n          padding: '20px',\n          overflowY: 'auto'\n        },\n        children: renderTabContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1339,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#2d2d44',\n        padding: '15px 20px',\n        borderTop: '2px solid #4a9eff',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: message && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#ffd43b',\n            fontSize: '1rem'\n          },\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1386,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: advanceTurn,\n        disabled: actionTaken,\n        style: {\n          background: actionTaken ? '#666' : '#4a9eff',\n          color: '#fff',\n          border: 'none',\n          padding: '12px 30px',\n          fontSize: '1.1rem',\n          borderRadius: '5px',\n          cursor: actionTaken ? 'not-allowed' : 'pointer',\n          fontWeight: 'bold'\n        },\n        children: actionTaken ? '⏳ Processing...' : '▶️ End Turn'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1376,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1242,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"Jh2Pc04bz3FUjIIPnh0mNG6NoIk=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "WIN_CONDITIONS", "treasury", "prestige", "provinces", "LOSE_CONDITIONS", "stability", "legitimacy", "App", "_s", "countries", "setCountries", "setProvinces", "playerCountry", "setPlayerCountry", "turn", "setTurn", "message", "setMessage", "gamePhase", "setGamePhase", "globalResources", "setGlobalResources", "technologies", "setTechnologies", "militaryData", "setMilitaryData", "unitTypes", "setUnitTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedProvince", "activeTab", "setActiveTab", "actionTaken", "setActionTaken", "event", "setEvent", "notifications", "setNotifications", "loadGameData", "countriesRes", "provincesRes", "resourcesRes", "techRes", "unitTypesRes", "Promise", "all", "get", "console", "log", "data", "length", "resources", "Object", "keys", "error", "fetchTurnEvents", "eventRes", "encodeURIComponent", "name", "selectCountry", "country", "addNotification", "ruler_name", "militaryRes", "text", "type", "notification", "id", "Date", "now", "timestamp", "toLocaleTimeString", "prev", "slice", "advanceTurn", "response", "post", "global_resources", "updatedPlayerCountry", "find", "c", "checkGameEnd", "startResearch", "category", "techName", "technology", "success", "countryRes", "renderTabContent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "gridTemplateColumns", "gap", "marginBottom", "background", "padding", "borderRadius", "monthly_income", "toFixed", "monthly_expenses", "inflation", "army_size", "toLocaleString", "navy_size", "military_tradition", "war_exhaustion", "filter", "p", "owner", "map", "province", "cursor", "border", "onClick", "development", "population_groups", "reduce", "sum", "pop", "size", "title", "description", "color", "effect", "totalPop", "avgHappiness", "happiness", "avgMilitancy", "militancy", "margin", "fontSize", "unrest", "height", "marginTop", "popGroup", "index", "textTransform", "social_class", "replace", "culture", "religion", "profession", "wealth", "justifyContent", "overflow", "width", "transition", "consciousness", "disabled", "allPops", "flatMap", "classCounts", "cultureCounts", "religionCounts", "for<PERSON>ach", "entries", "className", "count", "trade_efficiency", "resource", "textAlign", "current_price", "opacity", "base_value", "fontWeight", "units", "u", "includes", "unit_type", "military_expenses", "unit", "alignItems", "marginLeft", "location", "maintenance_cost", "strength", "morale", "experience", "unitType", "stats", "cost", "maintenance", "speed", "capital", "recruitUnit", "research_points", "points", "current_research", "tech", "isResearched", "canResearch", "prerequisites", "every", "prereq", "isCurrentlyResearching", "values", "join", "minHeight", "onSelectCountry", "selectedCountry", "max<PERSON><PERSON><PERSON>", "onMouseEnter", "e", "target", "borderColor", "onMouseLeave", "government_type", "window", "reload", "borderBottom", "tab", "flex", "onSelectProvince", "maxHeight", "overflowY", "terrain", "borderTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\n// Enhanced game constants\r\nconst WIN_CONDITIONS = {\r\n  treasury: 10000,\r\n  prestige: 100,\r\n  provinces: 10\r\n};\r\n\r\nconst LOSE_CONDITIONS = {\r\n  stability: 10,\r\n  treasury: -5000,\r\n  legitimacy: 20\r\n};\r\n\r\nexport default function App() {\r\n  // Core game state\r\n  const [countries, setCountries] = useState([]);\r\n  const [provinces, setProvinces] = useState([]);\r\n  const [playerCountry, setPlayerCountry] = useState(null);\r\n  const [turn, setTurn] = useState(1);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [gamePhase, setGamePhase] = useState(\"loading\"); // loading, country_selection, playing, victory, defeat\r\n\r\n  // Enhanced game data\r\n  const [globalResources, setGlobalResources] = useState({});\r\n  const [technologies, setTechnologies] = useState({});\r\n  const [militaryData, setMilitaryData] = useState({});\r\n  const [unitTypes, setUnitTypes] = useState({});\r\n  const [selectedProvince, setSelectedProvince] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(\"overview\"); // overview, population, economy, military, diplomacy, technology\r\n\r\n  // UI state\r\n  const [actionTaken, setActionTaken] = useState(false);\r\n  const [event, setEvent] = useState(null);\r\n  const [notifications, setNotifications] = useState([]);\r\n\r\n  // Load initial game data\r\n  useEffect(() => {\r\n    const loadGameData = async () => {\r\n      try {\r\n        setGamePhase(\"loading\");\r\n\r\n        // Load all game data in parallel\r\n        const [countriesRes, provincesRes, resourcesRes, techRes, unitTypesRes] = await Promise.all([\r\n          axios.get(\"http://localhost:8000/countries\"),\r\n          axios.get(\"http://localhost:8000/provinces\"),\r\n          axios.get(\"http://localhost:8000/resources\"),\r\n          axios.get(\"http://localhost:8000/technologies\"),\r\n          axios.get(\"http://localhost:8000/military/unit_types\")\r\n        ]);\r\n\r\n        console.log(\"Game data loaded:\", {\r\n          countries: countriesRes.data.length,\r\n          provinces: provincesRes.data.length,\r\n          resources: Object.keys(resourcesRes.data).length,\r\n          technologies: Object.keys(techRes.data).length,\r\n          unitTypes: Object.keys(unitTypesRes.data).length\r\n        });\r\n\r\n        setCountries(countriesRes.data);\r\n        setProvinces(provincesRes.data);\r\n        setGlobalResources(resourcesRes.data);\r\n        setTechnologies(techRes.data);\r\n        setUnitTypes(unitTypesRes.data);\r\n        setGamePhase(\"country_selection\");\r\n\r\n      } catch (error) {\r\n        console.error(\"Failed to load game data:\", error);\r\n        setMessage(\"Failed to load game data. Please refresh the page.\");\r\n      }\r\n    };\r\n\r\n    loadGameData();\r\n  }, []);\r\n\r\n  // Fetch events when turn changes\r\n  useEffect(() => {\r\n    if (!playerCountry || gamePhase !== \"playing\") return;\r\n\r\n    const fetchTurnEvents = async () => {\r\n      try {\r\n        const eventRes = await axios.get(`http://localhost:8000/event?country=${encodeURIComponent(playerCountry.name)}`);\r\n        setEvent(eventRes.data);\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch events:\", error);\r\n      }\r\n    };\r\n\r\n    fetchTurnEvents();\r\n  }, [turn, playerCountry, gamePhase]);\r\n\r\n  // Game phase handlers\r\n  const selectCountry = async (country) => {\r\n    setPlayerCountry(country);\r\n    setGamePhase(\"playing\");\r\n    setActiveTab(\"overview\");\r\n    addNotification(`Welcome, ${country.ruler_name} of ${country.name}!`, \"success\");\r\n\r\n    // Load military data for the selected country\r\n    try {\r\n      const militaryRes = await axios.get(`http://localhost:8000/military/${country.name}`);\r\n      setMilitaryData(militaryRes.data);\r\n    } catch (error) {\r\n      console.error(\"Failed to load military data:\", error);\r\n    }\r\n  };\r\n\r\n  const addNotification = (text, type = \"info\") => {\r\n    const notification = {\r\n      id: Date.now(),\r\n      text,\r\n      type,\r\n      timestamp: new Date().toLocaleTimeString()\r\n    };\r\n    setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications\r\n  };\r\n\r\n  // Enhanced turn advancement\r\n  const advanceTurn = async () => {\r\n    try {\r\n      setMessage(\"Processing turn...\");\r\n      const response = await axios.post(\"http://localhost:8000/turn\");\r\n\r\n      // Update all game state\r\n      setTurn(response.data.turn);\r\n      setCountries(response.data.countries);\r\n      setProvinces(response.data.provinces);\r\n      setGlobalResources(response.data.global_resources);\r\n\r\n      // Update player country reference\r\n      const updatedPlayerCountry = response.data.countries.find(c => c.name === playerCountry.name);\r\n      if (updatedPlayerCountry) {\r\n        setPlayerCountry(updatedPlayerCountry);\r\n\r\n        // Refresh military data\r\n        try {\r\n          const militaryRes = await axios.get(`http://localhost:8000/military/${updatedPlayerCountry.name}`);\r\n          setMilitaryData(militaryRes.data);\r\n        } catch (error) {\r\n          console.error(\"Failed to refresh military data:\", error);\r\n        }\r\n      }\r\n\r\n      setActionTaken(false);\r\n      setMessage(\"\");\r\n      addNotification(`Turn ${response.data.turn} begins`, \"info\");\r\n\r\n      // Check win/lose conditions\r\n      checkGameEnd(updatedPlayerCountry);\r\n\r\n    } catch (error) {\r\n      console.error(\"Failed to advance turn:\", error);\r\n      setMessage(\"Failed to advance turn. Please try again.\");\r\n    }\r\n  };\r\n\r\n  const checkGameEnd = (country) => {\r\n    if (!country) return;\r\n\r\n    // Check victory conditions\r\n    if (country.treasury >= WIN_CONDITIONS.treasury ||\r\n        country.prestige >= WIN_CONDITIONS.prestige ||\r\n        country.provinces.length >= WIN_CONDITIONS.provinces) {\r\n      setGamePhase(\"victory\");\r\n      return;\r\n    }\r\n\r\n    // Check defeat conditions\r\n    if (country.stability <= LOSE_CONDITIONS.stability ||\r\n        country.treasury <= LOSE_CONDITIONS.treasury ||\r\n        country.legitimacy <= LOSE_CONDITIONS.legitimacy) {\r\n      setGamePhase(\"defeat\");\r\n      return;\r\n    }\r\n  };\r\n\r\n  // Research technology\r\n  const startResearch = async (category, techName) => {\r\n    try {\r\n      const response = await axios.post(\"http://localhost:8000/research\", {\r\n        country: playerCountry.name,\r\n        category,\r\n        technology: techName\r\n      });\r\n\r\n      if (response.data.success) {\r\n        addNotification(response.data.message, \"success\");\r\n        // Refresh country data\r\n        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);\r\n        setPlayerCountry(countryRes.data);\r\n      } else {\r\n        addNotification(response.data.error, \"error\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Research failed:\", error);\r\n      addNotification(\"Failed to start research\", \"error\");\r\n    }\r\n  };\r\n\r\n  // Tab content renderer\r\n  const renderTabContent = () => {\r\n    switch (activeTab) {\r\n      case 'overview':\r\n        return (\r\n          <div>\r\n            <h2>🏛️ Nation Overview</h2>\r\n\r\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n                <h3>💰 Economy</h3>\r\n                <p><strong>Monthly Income:</strong> {playerCountry.monthly_income.toFixed(1)} gold</p>\r\n                <p><strong>Monthly Expenses:</strong> {playerCountry.monthly_expenses.toFixed(1)} gold</p>\r\n                <p><strong>Net Income:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>\r\n                <p><strong>Inflation:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>\r\n              </div>\r\n\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n                <h3>⚔️ Military</h3>\r\n                <p><strong>Army Size:</strong> {playerCountry.army_size.toLocaleString()} troops</p>\r\n                <p><strong>Navy Size:</strong> {playerCountry.navy_size} ships</p>\r\n                <p><strong>Military Tradition:</strong> {playerCountry.military_tradition.toFixed(1)}</p>\r\n                <p><strong>War Exhaustion:</strong> {playerCountry.war_exhaustion.toFixed(1)}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🏛️ Provinces ({playerCountry.provinces.length})</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>\r\n                {provinces.filter(p => p.owner === playerCountry.name).map(province => (\r\n                  <div\r\n                    key={province.name}\r\n                    style={{\r\n                      background: '#1a1a2e',\r\n                      padding: '10px',\r\n                      borderRadius: '5px',\r\n                      cursor: 'pointer',\r\n                      border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'\r\n                    }}\r\n                    onClick={() => setSelectedProvince(province)}\r\n                  >\r\n                    <strong>{province.name}</strong>\r\n                    <br />\r\n                    <small>Dev: {province.development.toFixed(1)} | Pop: {province.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</small>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Recent Events */}\r\n            {event && (\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n                <h3>📰 Current Event</h3>\r\n                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>\r\n                  <h4>{event.title}</h4>\r\n                  <p>{event.description}</p>\r\n                  <p style={{ color: '#4a9eff' }}><strong>Effect:</strong> {event.effect}</p>\r\n                  <button\r\n                    onClick={() => {\r\n                      // Apply event logic here\r\n                      addNotification(`Applied: ${event.effect}`, \"success\");\r\n                      setEvent(null);\r\n                    }}\r\n                    style={{\r\n                      background: '#4a9eff',\r\n                      color: '#fff',\r\n                      border: 'none',\r\n                      padding: '8px 15px',\r\n                      borderRadius: '5px',\r\n                      cursor: 'pointer'\r\n                    }}\r\n                  >\r\n                    Accept\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        );\r\n\r\n      case 'population':\r\n        return (\r\n          <div>\r\n            <h2>👥 Population Management</h2>\r\n\r\n            {/* Population Overview */}\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>📊 Population Summary</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>\r\n                {provinces.filter(p => p.owner === playerCountry.name).map(province => {\r\n                  const totalPop = province.population_groups.reduce((sum, pop) => sum + pop.size, 0);\r\n                  const avgHappiness = province.population_groups.reduce((sum, pop) => sum + pop.happiness, 0) / province.population_groups.length;\r\n                  const avgMilitancy = province.population_groups.reduce((sum, pop) => sum + pop.militancy, 0) / province.population_groups.length;\r\n\r\n                  return (\r\n                    <div\r\n                      key={province.name}\r\n                      style={{\r\n                        background: '#1a1a2e',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: 'pointer',\r\n                        border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'\r\n                      }}\r\n                      onClick={() => setSelectedProvince(province)}\r\n                    >\r\n                      <h4 style={{ margin: '0 0 8px 0' }}>{province.name}</h4>\r\n                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>\r\n                        <strong>Population:</strong> {totalPop.toLocaleString()}\r\n                      </p>\r\n                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>\r\n                        <strong>Happiness:</strong> {avgHappiness.toFixed(1)}/10\r\n                      </p>\r\n                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>\r\n                        <strong>Unrest:</strong> {province.unrest.toFixed(1)}\r\n                      </p>\r\n                      <div style={{\r\n                        background: avgHappiness > 6 ? '#51cf66' : avgHappiness > 4 ? '#ffd43b' : '#ff6b6b',\r\n                        height: '4px',\r\n                        borderRadius: '2px',\r\n                        marginTop: '8px'\r\n                      }} />\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Detailed Province Population */}\r\n            {selectedProvince && (\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n                <h3>🏘️ {selectedProvince.name} - Population Details</h3>\r\n\r\n                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\r\n                  {selectedProvince.population_groups.map((popGroup, index) => (\r\n                    <div\r\n                      key={index}\r\n                      style={{\r\n                        background: '#1a1a2e',\r\n                        padding: '15px',\r\n                        borderRadius: '5px',\r\n                        border: '1px solid #444'\r\n                      }}\r\n                    >\r\n                      <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize' }}>\r\n                        {popGroup.social_class.replace('_', ' ')}\r\n                      </h4>\r\n\r\n                      <div style={{ fontSize: '0.9rem' }}>\r\n                        <p><strong>Size:</strong> {popGroup.size.toLocaleString()}</p>\r\n                        <p><strong>Culture:</strong> {popGroup.culture}</p>\r\n                        <p><strong>Religion:</strong> {popGroup.religion}</p>\r\n                        <p><strong>Profession:</strong> {popGroup.profession}</p>\r\n                        <p><strong>Wealth:</strong> {popGroup.wealth.toFixed(1)}</p>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Happiness:</span>\r\n                            <span style={{ color: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b' }}>\r\n                              {popGroup.happiness.toFixed(1)}/10\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b',\r\n                              height: '100%',\r\n                              width: `${(popGroup.happiness / 10) * 100}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Militancy:</span>\r\n                            <span style={{ color: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66' }}>\r\n                              {popGroup.militancy.toFixed(1)}/10\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66',\r\n                              height: '100%',\r\n                              width: `${(popGroup.militancy / 10) * 100}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Consciousness:</span>\r\n                            <span style={{ color: '#4a9eff' }}>\r\n                              {popGroup.consciousness.toFixed(1)}/10\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: '#4a9eff',\r\n                              height: '100%',\r\n                              width: `${(popGroup.consciousness / 10) * 100}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Population Actions */}\r\n                <div style={{ marginTop: '20px' }}>\r\n                  <h4>👑 Population Policies</h4>\r\n                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>\r\n                    <button\r\n                      onClick={() => {\r\n                        if (!actionTaken) {\r\n                          addNotification(\"Implemented education reforms\", \"success\");\r\n                          setActionTaken(true);\r\n                        }\r\n                      }}\r\n                      disabled={actionTaken}\r\n                      style={{\r\n                        background: actionTaken ? '#666' : '#4a9eff',\r\n                        color: '#fff',\r\n                        border: 'none',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      📚 Education Reform<br />\r\n                      <small>Increase consciousness</small>\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => {\r\n                        if (!actionTaken) {\r\n                          addNotification(\"Promoted cultural integration\", \"success\");\r\n                          setActionTaken(true);\r\n                        }\r\n                      }}\r\n                      disabled={actionTaken}\r\n                      style={{\r\n                        background: actionTaken ? '#666' : '#51cf66',\r\n                        color: '#fff',\r\n                        border: 'none',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      🤝 Cultural Integration<br />\r\n                      <small>Reduce cultural tensions</small>\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => {\r\n                        if (!actionTaken) {\r\n                          addNotification(\"Implemented welfare programs\", \"success\");\r\n                          setActionTaken(true);\r\n                        }\r\n                      }}\r\n                      disabled={actionTaken}\r\n                      style={{\r\n                        background: actionTaken ? '#666' : '#ffd43b',\r\n                        color: '#000',\r\n                        border: 'none',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      🏥 Welfare Programs<br />\r\n                      <small>Increase happiness</small>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* National Demographics */}\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n              <h3>🌍 National Demographics</h3>\r\n\r\n              {(() => {\r\n                const allPops = provinces\r\n                  .filter(p => p.owner === playerCountry.name)\r\n                  .flatMap(p => p.population_groups);\r\n\r\n                const totalPop = allPops.reduce((sum, pop) => sum + pop.size, 0);\r\n                const classCounts = {};\r\n                const cultureCounts = {};\r\n                const religionCounts = {};\r\n\r\n                allPops.forEach(pop => {\r\n                  classCounts[pop.social_class] = (classCounts[pop.social_class] || 0) + pop.size;\r\n                  cultureCounts[pop.culture] = (cultureCounts[pop.culture] || 0) + pop.size;\r\n                  religionCounts[pop.religion] = (religionCounts[pop.religion] || 0) + pop.size;\r\n                });\r\n\r\n                return (\r\n                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>\r\n                    <div>\r\n                      <h4>👑 Social Classes</h4>\r\n                      {Object.entries(classCounts).map(([className, count]) => (\r\n                        <div key={className} style={{ margin: '8px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>\r\n                            <span style={{ textTransform: 'capitalize' }}>{className.replace('_', ' ')}</span>\r\n                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>\r\n                          </div>\r\n                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>\r\n                            <div style={{\r\n                              background: '#4a9eff',\r\n                              height: '100%',\r\n                              width: `${(count / totalPop) * 100}%`\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n\r\n                    <div>\r\n                      <h4>🏛️ Cultures</h4>\r\n                      {Object.entries(cultureCounts).map(([culture, count]) => (\r\n                        <div key={culture} style={{ margin: '8px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>\r\n                            <span style={{ textTransform: 'capitalize' }}>{culture}</span>\r\n                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>\r\n                          </div>\r\n                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>\r\n                            <div style={{\r\n                              background: '#51cf66',\r\n                              height: '100%',\r\n                              width: `${(count / totalPop) * 100}%`\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n\r\n                    <div>\r\n                      <h4>⛪ Religions</h4>\r\n                      {Object.entries(religionCounts).map(([religion, count]) => (\r\n                        <div key={religion} style={{ margin: '8px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>\r\n                            <span style={{ textTransform: 'capitalize' }}>{religion}</span>\r\n                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>\r\n                          </div>\r\n                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>\r\n                            <div style={{\r\n                              background: '#ffd43b',\r\n                              height: '100%',\r\n                              width: `${(count / totalPop) * 100}%`\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 'economy':\r\n        return (\r\n          <div>\r\n            <h2>💰 Economic Management</h2>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>📊 Financial Summary</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>\r\n                <div>\r\n                  <p><strong>Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>\r\n                  <p><strong>Monthly Income:</strong> +{playerCountry.monthly_income.toFixed(1)} gold</p>\r\n                  <p><strong>Monthly Expenses:</strong> -{playerCountry.monthly_expenses.toFixed(1)} gold</p>\r\n                  <p><strong>Net Balance:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>\r\n                </div>\r\n                <div>\r\n                  <p><strong>Trade Efficiency:</strong> {(playerCountry.trade_efficiency * 100).toFixed(1)}%</p>\r\n                  <p><strong>Inflation Rate:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🌍 Global Resource Prices</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px' }}>\r\n                {Object.entries(globalResources).map(([name, resource]) => (\r\n                  <div key={name} style={{ background: '#1a1a2e', padding: '10px', borderRadius: '5px', textAlign: 'center' }}>\r\n                    <strong>{name.replace('_', ' ')}</strong>\r\n                    <br />\r\n                    <span style={{ color: '#4a9eff' }}>{resource.current_price.toFixed(2)} gold</span>\r\n                    <br />\r\n                    <small style={{ opacity: 0.7 }}>\r\n                      {resource.current_price > resource.base_value ? '📈' : '📉'}\r\n                    </small>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n              <h3>🏭 Economic Actions</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>\r\n                <button\r\n                  onClick={() => {\r\n                    if (!actionTaken) {\r\n                      addNotification(\"Invested in infrastructure development\", \"success\");\r\n                      setActionTaken(true);\r\n                    }\r\n                  }}\r\n                  disabled={actionTaken}\r\n                  style={{\r\n                    background: actionTaken ? '#666' : '#4a9eff',\r\n                    color: '#fff',\r\n                    border: 'none',\r\n                    padding: '15px',\r\n                    borderRadius: '5px',\r\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                  }}\r\n                >\r\n                  🏗️ Build Infrastructure<br />\r\n                  <small>Cost: 200 gold</small>\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => {\r\n                    if (!actionTaken) {\r\n                      addNotification(\"Promoted trade and commerce\", \"success\");\r\n                      setActionTaken(true);\r\n                    }\r\n                  }}\r\n                  disabled={actionTaken}\r\n                  style={{\r\n                    background: actionTaken ? '#666' : '#51cf66',\r\n                    color: '#fff',\r\n                    border: 'none',\r\n                    padding: '15px',\r\n                    borderRadius: '5px',\r\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                  }}\r\n                >\r\n                  🏪 Promote Trade<br />\r\n                  <small>Cost: 150 gold</small>\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => {\r\n                    if (!actionTaken) {\r\n                      addNotification(\"Implemented tax reforms\", \"success\");\r\n                      setActionTaken(true);\r\n                    }\r\n                  }}\r\n                  disabled={actionTaken}\r\n                  style={{\r\n                    background: actionTaken ? '#666' : '#ffd43b',\r\n                    color: '#000',\r\n                    border: 'none',\r\n                    padding: '15px',\r\n                    borderRadius: '5px',\r\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                  }}\r\n                >\r\n                  📋 Tax Reform<br />\r\n                  <small>Cost: 100 gold</small>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 'military':\r\n        return (\r\n          <div>\r\n            <h2>⚔️ Military Command</h2>\r\n\r\n            {/* Military Overview */}\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🏛️ Military Summary</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>\r\n                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>\r\n                  <h4 style={{ margin: '0 0 10px 0' }}>🪖 Total Army</h4>\r\n                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#4a9eff' }}>\r\n                    {militaryData.units ? militaryData.units.filter(u => !['frigate', 'ship_of_line'].includes(u.unit_type)).reduce((sum, u) => sum + u.size, 0).toLocaleString() : '0'}\r\n                  </div>\r\n                  <small>troops</small>\r\n                </div>\r\n\r\n                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>\r\n                  <h4 style={{ margin: '0 0 10px 0' }}>⛵ Total Navy</h4>\r\n                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#51cf66' }}>\r\n                    {militaryData.units ? militaryData.units.filter(u => ['frigate', 'ship_of_line'].includes(u.unit_type)).length : '0'}\r\n                  </div>\r\n                  <small>ships</small>\r\n                </div>\r\n\r\n                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>\r\n                  <h4 style={{ margin: '0 0 10px 0' }}>🎖️ Military Tradition</h4>\r\n                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ffd43b' }}>\r\n                    {militaryData.military_tradition ? militaryData.military_tradition.toFixed(1) : '0.0'}\r\n                  </div>\r\n                  <small>/ 100</small>\r\n                </div>\r\n\r\n                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px', textAlign: 'center' }}>\r\n                  <h4 style={{ margin: '0 0 10px 0' }}>💰 Military Expenses</h4>\r\n                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ff6b6b' }}>\r\n                    {militaryData.military_expenses ? militaryData.military_expenses.toFixed(1) : '0.0'}\r\n                  </div>\r\n                  <small>gold/month</small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Military Units */}\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🪖 Military Units</h3>\r\n\r\n              {militaryData.units && militaryData.units.length > 0 ? (\r\n                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>\r\n                  {militaryData.units.map((unit, index) => (\r\n                    <div\r\n                      key={index}\r\n                      style={{\r\n                        background: '#1a1a2e',\r\n                        padding: '15px',\r\n                        borderRadius: '5px',\r\n                        border: '1px solid #444'\r\n                      }}\r\n                    >\r\n                      <h4 style={{ margin: '0 0 10px 0', display: 'flex', alignItems: 'center' }}>\r\n                        {unit.unit_type === 'infantry' && '🪖'}\r\n                        {unit.unit_type === 'cavalry' && '🐎'}\r\n                        {unit.unit_type === 'artillery' && '💣'}\r\n                        {unit.unit_type === 'frigate' && '⛵'}\r\n                        {unit.unit_type === 'ship_of_line' && '🚢'}\r\n                        <span style={{ marginLeft: '8px' }}>{unit.name}</span>\r\n                      </h4>\r\n\r\n                      <div style={{ fontSize: '0.9rem' }}>\r\n                        <p><strong>Type:</strong> {unit.unit_type.replace('_', ' ')}</p>\r\n                        <p><strong>Size:</strong> {unit.size.toLocaleString()}</p>\r\n                        <p><strong>Location:</strong> {unit.location}</p>\r\n                        <p><strong>Maintenance:</strong> {unit.maintenance_cost.toFixed(1)} gold/month</p>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Strength:</span>\r\n                            <span style={{ color: unit.strength > 80 ? '#51cf66' : unit.strength > 50 ? '#ffd43b' : '#ff6b6b' }}>\r\n                              {unit.strength.toFixed(1)}%\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: unit.strength > 80 ? '#51cf66' : unit.strength > 50 ? '#ffd43b' : '#ff6b6b',\r\n                              height: '100%',\r\n                              width: `${unit.strength}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Morale:</span>\r\n                            <span style={{ color: unit.morale > 70 ? '#51cf66' : unit.morale > 40 ? '#ffd43b' : '#ff6b6b' }}>\r\n                              {unit.morale.toFixed(1)}%\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: unit.morale > 70 ? '#51cf66' : unit.morale > 40 ? '#ffd43b' : '#ff6b6b',\r\n                              height: '100%',\r\n                              width: `${unit.morale}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Experience:</span>\r\n                            <span style={{ color: '#4a9eff' }}>\r\n                              {unit.experience.toFixed(1)}%\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: '#4a9eff',\r\n                              height: '100%',\r\n                              width: `${unit.experience}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <p style={{ textAlign: 'center', color: '#888', padding: '20px' }}>\r\n                  No military units. Recruit some units to defend your nation!\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Unit Recruitment */}\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n              <h3>🏭 Unit Recruitment</h3>\r\n\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\r\n                {Object.entries(unitTypes).map(([unitType, stats]) => (\r\n                  <div\r\n                    key={unitType}\r\n                    style={{\r\n                      background: '#1a1a2e',\r\n                      padding: '15px',\r\n                      borderRadius: '5px',\r\n                      border: '1px solid #444'\r\n                    }}\r\n                  >\r\n                    <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize', display: 'flex', alignItems: 'center' }}>\r\n                      {unitType === 'infantry' && '🪖'}\r\n                      {unitType === 'cavalry' && '🐎'}\r\n                      {unitType === 'artillery' && '💣'}\r\n                      {unitType === 'frigate' && '⛵'}\r\n                      {unitType === 'ship_of_line' && '🚢'}\r\n                      <span style={{ marginLeft: '8px' }}>{unitType.replace('_', ' ')}</span>\r\n                    </h4>\r\n\r\n                    <div style={{ fontSize: '0.9rem', marginBottom: '15px' }}>\r\n                      <p><strong>Cost:</strong> {stats.cost} gold</p>\r\n                      <p><strong>Maintenance:</strong> {stats.maintenance} gold/month</p>\r\n                      <p><strong>Strength:</strong> {stats.strength}</p>\r\n                      <p><strong>Speed:</strong> {stats.speed}</p>\r\n                    </div>\r\n\r\n                    <button\r\n                      onClick={() => {\r\n                        if (!actionTaken && playerCountry.provinces.length > 0) {\r\n                          const capital = playerCountry.provinces[0];\r\n                          recruitUnit(unitType, capital, 1000);\r\n                        }\r\n                      }}\r\n                      disabled={actionTaken || playerCountry.treasury < stats.cost}\r\n                      style={{\r\n                        background: (actionTaken || playerCountry.treasury < stats.cost) ? '#666' : '#4a9eff',\r\n                        color: '#fff',\r\n                        border: 'none',\r\n                        padding: '10px 15px',\r\n                        borderRadius: '5px',\r\n                        cursor: (actionTaken || playerCountry.treasury < stats.cost) ? 'not-allowed' : 'pointer',\r\n                        width: '100%',\r\n                        fontSize: '0.9rem'\r\n                      }}\r\n                    >\r\n                      {playerCountry.treasury < stats.cost ? 'Insufficient Funds' :\r\n                       actionTaken ? 'Action Taken' : `Recruit ${unitType.replace('_', ' ')}`}\r\n                    </button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div style={{ marginTop: '20px', padding: '15px', background: '#1a1a2e', borderRadius: '5px' }}>\r\n                <h4>⚡ Military Actions</h4>\r\n                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>\r\n                  <button\r\n                    onClick={() => {\r\n                      if (!actionTaken) {\r\n                        addNotification(\"Conducted military training exercises\", \"success\");\r\n                        setActionTaken(true);\r\n                      }\r\n                    }}\r\n                    disabled={actionTaken}\r\n                    style={{\r\n                      background: actionTaken ? '#666' : '#51cf66',\r\n                      color: '#fff',\r\n                      border: 'none',\r\n                      padding: '12px',\r\n                      borderRadius: '5px',\r\n                      cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                    }}\r\n                  >\r\n                    🎯 Military Training<br />\r\n                    <small>Improve unit experience</small>\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => {\r\n                      if (!actionTaken) {\r\n                        addNotification(\"Reorganized military structure\", \"success\");\r\n                        setActionTaken(true);\r\n                      }\r\n                    }}\r\n                    disabled={actionTaken}\r\n                    style={{\r\n                      background: actionTaken ? '#666' : '#ffd43b',\r\n                      color: '#000',\r\n                      border: 'none',\r\n                      padding: '12px',\r\n                      borderRadius: '5px',\r\n                      cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                    }}\r\n                  >\r\n                    📋 Military Reform<br />\r\n                    <small>Reduce maintenance costs</small>\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => {\r\n                      if (!actionTaken) {\r\n                        addNotification(\"Fortified border provinces\", \"success\");\r\n                        setActionTaken(true);\r\n                      }\r\n                    }}\r\n                    disabled={actionTaken}\r\n                    style={{\r\n                      background: actionTaken ? '#666' : '#ff6b6b',\r\n                      color: '#fff',\r\n                      border: 'none',\r\n                      padding: '12px',\r\n                      borderRadius: '5px',\r\n                      cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                    }}\r\n                  >\r\n                    🏰 Build Fortifications<br />\r\n                    <small>Improve defense</small>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 'technology':\r\n        return (\r\n          <div>\r\n            <h2>🔬 Technology & Research</h2>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>📚 Research Progress</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>\r\n                {Object.entries(playerCountry.research_points).map(([category, points]) => (\r\n                  <div key={category} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>\r\n                    <h4 style={{ textTransform: 'capitalize', margin: '0 0 10px 0' }}>\r\n                      {category === 'military' && '⚔️'}\r\n                      {category === 'economic' && '💰'}\r\n                      {category === 'social' && '👥'}\r\n                      {category === 'administrative' && '🏛️'}\r\n                      {' ' + category}\r\n                    </h4>\r\n                    <p><strong>Points:</strong> {points.toFixed(1)}</p>\r\n                    {playerCountry.current_research[category] && (\r\n                      <p><strong>Researching:</strong> {playerCountry.current_research[category]}</p>\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🧪 Available Technologies</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>\r\n                {Object.entries(technologies).map(([name, tech]) => {\r\n                  const isResearched = playerCountry.technologies.includes(name);\r\n                  const canResearch = tech.prerequisites.every(prereq => playerCountry.technologies.includes(prereq));\r\n                  const isCurrentlyResearching = Object.values(playerCountry.current_research).includes(name);\r\n\r\n                  return (\r\n                    <div\r\n                      key={name}\r\n                      style={{\r\n                        background: isResearched ? '#2d5a2d' : '#1a1a2e',\r\n                        padding: '15px',\r\n                        borderRadius: '5px',\r\n                        border: isCurrentlyResearching ? '2px solid #4a9eff' : '1px solid #444',\r\n                        opacity: canResearch || isResearched ? 1 : 0.5\r\n                      }}\r\n                    >\r\n                      <h4 style={{ margin: '0 0 10px 0' }}>{name}</h4>\r\n                      <p style={{ fontSize: '0.9rem', margin: '5px 0' }}>{tech.description}</p>\r\n                      <p><strong>Category:</strong> {tech.category}</p>\r\n                      <p><strong>Cost:</strong> {tech.cost} points</p>\r\n\r\n                      {tech.prerequisites.length > 0 && (\r\n                        <p><strong>Prerequisites:</strong> {tech.prerequisites.join(', ')}</p>\r\n                      )}\r\n\r\n                      {isResearched && (\r\n                        <span style={{ color: '#51cf66', fontWeight: 'bold' }}>✅ Researched</span>\r\n                      )}\r\n\r\n                      {!isResearched && canResearch && !isCurrentlyResearching && (\r\n                        <button\r\n                          onClick={() => startResearch(tech.category, name)}\r\n                          style={{\r\n                            background: '#4a9eff',\r\n                            color: '#fff',\r\n                            border: 'none',\r\n                            padding: '8px 15px',\r\n                            borderRadius: '5px',\r\n                            cursor: 'pointer',\r\n                            marginTop: '10px'\r\n                          }}\r\n                        >\r\n                          Start Research\r\n                        </button>\r\n                      )}\r\n\r\n                      {isCurrentlyResearching && (\r\n                        <span style={{ color: '#ffd43b', fontWeight: 'bold' }}>🔬 Researching...</span>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      default:\r\n        return (\r\n          <div>\r\n            <h2>🚧 Coming Soon</h2>\r\n            <p>This feature is under development and will be available in future updates.</p>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  // Military recruitment\r\n  const recruitUnit = async (unitType, province, size = 1000) => {\r\n    try {\r\n      const response = await axios.post(\"http://localhost:8000/military/recruit\", {\r\n        country: playerCountry.name,\r\n        unit_type: unitType,\r\n        province: province,\r\n        size: size\r\n      });\r\n\r\n      if (response.data.success) {\r\n        addNotification(response.data.message, \"success\");\r\n\r\n        // Refresh military and country data\r\n        const [militaryRes, countryRes] = await Promise.all([\r\n          axios.get(`http://localhost:8000/military/${playerCountry.name}`),\r\n          axios.get(`http://localhost:8000/country/${playerCountry.name}`)\r\n        ]);\r\n\r\n        setMilitaryData(militaryRes.data);\r\n        setPlayerCountry(countryRes.data);\r\n        setActionTaken(true);\r\n      } else {\r\n        addNotification(response.data.error, \"error\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Recruitment failed:\", error);\r\n      addNotification(\"Failed to recruit unit\", \"error\");\r\n    }\r\n  };\r\n\r\n  // Game phase rendering\r\n  if (gamePhase === \"loading\") {\r\n    return (\r\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', background: '#1a1a2e' }}>\r\n        <div style={{ textAlign: 'center', color: '#fff' }}>\r\n          <h1>🏰 Empires & Revolutions</h1>\r\n          <p>Loading the world of Aeterra...</p>\r\n          <div style={{ margin: '20px 0' }}>⚔️ 🏛️ 💰 🔬 🌍</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (gamePhase === \"country_selection\") {\r\n    return (\r\n      <div style={{ background: '#1a1a2e', minHeight: '100vh', color: '#fff' }}>\r\n        <div style={{ textAlign: 'center', padding: '20px' }}>\r\n          <h1>🏰 Choose Your Destiny</h1>\r\n          <p>Select a nation to lead through the Age of Revolutions</p>\r\n        </div>\r\n\r\n        <WorldMap\r\n          onSelectCountry={selectCountry}\r\n          selectedCountry={null}\r\n          countries={countries}\r\n        />\r\n\r\n        <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>\r\n          <h2>Available Nations</h2>\r\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>\r\n            {countries.slice(0, 5).map(country => (\r\n              <div\r\n                key={country.name}\r\n                style={{\r\n                  background: '#2d2d44',\r\n                  padding: '20px',\r\n                  borderRadius: '10px',\r\n                  cursor: 'pointer',\r\n                  border: '2px solid transparent',\r\n                  transition: 'all 0.3s ease'\r\n                }}\r\n                onMouseEnter={(e) => e.target.style.borderColor = '#4a9eff'}\r\n                onMouseLeave={(e) => e.target.style.borderColor = 'transparent'}\r\n                onClick={() => selectCountry(country)}\r\n              >\r\n                <h3>{country.name}</h3>\r\n                <p><strong>Ruler:</strong> {country.ruler_name}</p>\r\n                <p><strong>Government:</strong> {country.government_type.replace('_', ' ')}</p>\r\n                <p><strong>Treasury:</strong> {country.treasury.toLocaleString()} gold</p>\r\n                <p><strong>Provinces:</strong> {country.provinces.length}</p>\r\n                <p><strong>Prestige:</strong> {country.prestige}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (gamePhase === \"victory\") {\r\n    return (\r\n      <div style={{\r\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n        minHeight: '100vh',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        color: '#fff',\r\n        textAlign: 'center'\r\n      }}>\r\n        <div>\r\n          <h1 style={{ fontSize: '4rem', marginBottom: '20px' }}>🎉 VICTORY! 🎉</h1>\r\n          <h2>The {playerCountry.name} Triumphant!</h2>\r\n          <p style={{ fontSize: '1.5rem', margin: '20px 0' }}>\r\n            Under the wise rule of {playerCountry.ruler_name}, your nation has achieved greatness!\r\n          </p>\r\n          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>\r\n            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>\r\n            <p><strong>Final Prestige:</strong> {playerCountry.prestige}</p>\r\n            <p><strong>Provinces Controlled:</strong> {playerCountry.provinces.length}</p>\r\n            <p><strong>Turns Survived:</strong> {turn}</p>\r\n          </div>\r\n          <button\r\n            onClick={() => window.location.reload()}\r\n            style={{\r\n              padding: '15px 30px',\r\n              fontSize: '1.2rem',\r\n              background: '#4a9eff',\r\n              color: '#fff',\r\n              border: 'none',\r\n              borderRadius: '5px',\r\n              cursor: 'pointer'\r\n            }}\r\n          >\r\n            Play Again\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (gamePhase === \"defeat\") {\r\n    return (\r\n      <div style={{\r\n        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',\r\n        minHeight: '100vh',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        color: '#fff',\r\n        textAlign: 'center'\r\n      }}>\r\n        <div>\r\n          <h1 style={{ fontSize: '4rem', marginBottom: '20px' }}>💀 DEFEAT 💀</h1>\r\n          <h2>The Fall of {playerCountry.name}</h2>\r\n          <p style={{ fontSize: '1.5rem', margin: '20px 0' }}>\r\n            The reign of {playerCountry.ruler_name} has come to an end...\r\n          </p>\r\n          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>\r\n            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>\r\n            <p><strong>Final Stability:</strong> {playerCountry.stability}</p>\r\n            <p><strong>Final Legitimacy:</strong> {playerCountry.legitimacy}</p>\r\n            <p><strong>Turns Survived:</strong> {turn}</p>\r\n          </div>\r\n          <button\r\n            onClick={() => window.location.reload()}\r\n            style={{\r\n              padding: '15px 30px',\r\n              fontSize: '1.2rem',\r\n              background: '#4a9eff',\r\n              color: '#fff',\r\n              border: 'none',\r\n              borderRadius: '5px',\r\n              cursor: 'pointer'\r\n            }}\r\n          >\r\n            Try Again\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Main game interface - sophisticated tabbed layout\r\n  return (\r\n    <div style={{ background: '#1a1a2e', minHeight: '100vh', color: '#fff' }}>\r\n      {/* Header */}\r\n      <div style={{\r\n        background: '#2d2d44',\r\n        padding: '15px 20px',\r\n        borderBottom: '2px solid #4a9eff',\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center'\r\n      }}>\r\n        <div>\r\n          <h1 style={{ margin: 0, fontSize: '1.8rem' }}>🏰 {playerCountry.name}</h1>\r\n          <p style={{ margin: 0, opacity: 0.8 }}>{playerCountry.ruler_name} • {playerCountry.government_type.replace('_', ' ')}</p>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>💰 {playerCountry.treasury.toLocaleString()}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Treasury</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>⭐ {playerCountry.prestige}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Prestige</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>🏛️ {playerCountry.stability}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Stability</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>👑 {playerCountry.legitimacy}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Legitimacy</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>📅 {turn}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Turn</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Notifications */}\r\n      {notifications.length > 0 && (\r\n        <div style={{ padding: '10px 20px' }}>\r\n          {notifications.map(notification => (\r\n            <div\r\n              key={notification.id}\r\n              style={{\r\n                background: notification.type === 'error' ? '#ff6b6b' :\r\n                           notification.type === 'success' ? '#51cf66' : '#4a9eff',\r\n                padding: '8px 15px',\r\n                margin: '5px 0',\r\n                borderRadius: '5px',\r\n                fontSize: '0.9rem',\r\n                display: 'flex',\r\n                justifyContent: 'space-between'\r\n              }}\r\n            >\r\n              <span>{notification.text}</span>\r\n              <span style={{ opacity: 0.7 }}>{notification.timestamp}</span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Tab Navigation */}\r\n      <div style={{\r\n        background: '#2d2d44',\r\n        padding: '0 20px',\r\n        borderBottom: '1px solid #444'\r\n      }}>\r\n        {['overview', 'population', 'economy', 'military', 'diplomacy', 'technology'].map(tab => (\r\n          <button\r\n            key={tab}\r\n            onClick={() => setActiveTab(tab)}\r\n            style={{\r\n              background: activeTab === tab ? '#4a9eff' : 'transparent',\r\n              color: '#fff',\r\n              border: 'none',\r\n              padding: '15px 25px',\r\n              margin: '0 5px',\r\n              cursor: 'pointer',\r\n              borderRadius: '5px 5px 0 0',\r\n              textTransform: 'capitalize',\r\n              fontSize: '1rem'\r\n            }}\r\n          >\r\n            {tab === 'overview' && '🏛️'}\r\n            {tab === 'population' && '👥'}\r\n            {tab === 'economy' && '💰'}\r\n            {tab === 'military' && '⚔️'}\r\n            {tab === 'diplomacy' && '🤝'}\r\n            {tab === 'technology' && '🔬'}\r\n            {' ' + tab}\r\n          </button>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Main Content Area */}\r\n      <div style={{ display: 'flex', height: 'calc(100vh - 200px)' }}>\r\n        {/* Left Panel - Map */}\r\n        <div style={{ flex: '1', background: '#16213e', padding: '20px' }}>\r\n          <WorldMap\r\n            onSelectCountry={() => {}}\r\n            selectedCountry={playerCountry}\r\n            countries={countries}\r\n            onSelectProvince={setSelectedProvince}\r\n            selectedProvince={selectedProvince}\r\n          />\r\n\r\n          {selectedProvince && (\r\n            <div style={{\r\n              background: '#2d2d44',\r\n              padding: '15px',\r\n              margin: '10px 0',\r\n              borderRadius: '8px',\r\n              maxHeight: '200px',\r\n              overflowY: 'auto'\r\n            }}>\r\n              <h3>{selectedProvince.name}</h3>\r\n              <p><strong>Owner:</strong> {selectedProvince.owner}</p>\r\n              <p><strong>Development:</strong> {selectedProvince.development.toFixed(1)}</p>\r\n              <p><strong>Population:</strong> {selectedProvince.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</p>\r\n              <p><strong>Terrain:</strong> {selectedProvince.terrain}</p>\r\n              <p><strong>Unrest:</strong> {selectedProvince.unrest.toFixed(1)}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Right Panel - Tab Content */}\r\n        <div style={{ flex: '1', background: '#1a1a2e', padding: '20px', overflowY: 'auto' }}>\r\n          {renderTabContent()}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Bottom Action Bar */}\r\n      <div style={{\r\n        background: '#2d2d44',\r\n        padding: '15px 20px',\r\n        borderTop: '2px solid #4a9eff',\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center'\r\n      }}>\r\n        <div>\r\n          {message && (\r\n            <span style={{ color: '#ffd43b', fontSize: '1rem' }}>{message}</span>\r\n          )}\r\n        </div>\r\n\r\n        <button\r\n          onClick={advanceTurn}\r\n          disabled={actionTaken}\r\n          style={{\r\n            background: actionTaken ? '#666' : '#4a9eff',\r\n            color: '#fff',\r\n            border: 'none',\r\n            padding: '12px 30px',\r\n            fontSize: '1.1rem',\r\n            borderRadius: '5px',\r\n            cursor: actionTaken ? 'not-allowed' : 'pointer',\r\n            fontWeight: 'bold'\r\n          }}\r\n        >\r\n          {actionTaken ? '⏳ Processing...' : '▶️ End Turn'}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,GAAG;EACbC,SAAS,EAAE;AACb,CAAC;AAED,MAAMC,eAAe,GAAG;EACtBC,SAAS,EAAE,EAAE;EACbJ,QAAQ,EAAE,CAAC,IAAI;EACfK,UAAU,EAAE;AACd,CAAC;AAED,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACQ,SAAS,EAAEQ,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;;EAExD;EACA,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACAD,SAAS,CAAC,MAAM;IACd,MAAM4C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFnB,YAAY,CAAC,SAAS,CAAC;;QAEvB;QACA,MAAM,CAACoB,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,OAAO,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1FjD,KAAK,CAACkD,GAAG,CAAC,iCAAiC,CAAC,EAC5ClD,KAAK,CAACkD,GAAG,CAAC,iCAAiC,CAAC,EAC5ClD,KAAK,CAACkD,GAAG,CAAC,iCAAiC,CAAC,EAC5ClD,KAAK,CAACkD,GAAG,CAAC,oCAAoC,CAAC,EAC/ClD,KAAK,CAACkD,GAAG,CAAC,2CAA2C,CAAC,CACvD,CAAC;QAEFC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/BvC,SAAS,EAAE8B,YAAY,CAACU,IAAI,CAACC,MAAM;UACnC/C,SAAS,EAAEqC,YAAY,CAACS,IAAI,CAACC,MAAM;UACnCC,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACZ,YAAY,CAACQ,IAAI,CAAC,CAACC,MAAM;UAChD5B,YAAY,EAAE8B,MAAM,CAACC,IAAI,CAACX,OAAO,CAACO,IAAI,CAAC,CAACC,MAAM;UAC9CxB,SAAS,EAAE0B,MAAM,CAACC,IAAI,CAACV,YAAY,CAACM,IAAI,CAAC,CAACC;QAC5C,CAAC,CAAC;QAEFxC,YAAY,CAAC6B,YAAY,CAACU,IAAI,CAAC;QAC/BtC,YAAY,CAAC6B,YAAY,CAACS,IAAI,CAAC;QAC/B5B,kBAAkB,CAACoB,YAAY,CAACQ,IAAI,CAAC;QACrC1B,eAAe,CAACmB,OAAO,CAACO,IAAI,CAAC;QAC7BtB,YAAY,CAACgB,YAAY,CAACM,IAAI,CAAC;QAC/B9B,YAAY,CAAC,mBAAmB,CAAC;MAEnC,CAAC,CAAC,OAAOmC,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDrC,UAAU,CAAC,oDAAoD,CAAC;MAClE;IACF,CAAC;IAEDqB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5C,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,aAAa,IAAIM,SAAS,KAAK,SAAS,EAAE;IAE/C,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM5D,KAAK,CAACkD,GAAG,CAAC,uCAAuCW,kBAAkB,CAAC7C,aAAa,CAAC8C,IAAI,CAAC,EAAE,CAAC;QACjHvB,QAAQ,CAACqB,QAAQ,CAACP,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACzC,IAAI,EAAEF,aAAa,EAAEM,SAAS,CAAC,CAAC;;EAEpC;EACA,MAAMyC,aAAa,GAAG,MAAOC,OAAO,IAAK;IACvC/C,gBAAgB,CAAC+C,OAAO,CAAC;IACzBzC,YAAY,CAAC,SAAS,CAAC;IACvBY,YAAY,CAAC,UAAU,CAAC;IACxB8B,eAAe,CAAC,YAAYD,OAAO,CAACE,UAAU,OAAOF,OAAO,CAACF,IAAI,GAAG,EAAE,SAAS,CAAC;;IAEhF;IACA,IAAI;MACF,MAAMK,WAAW,GAAG,MAAMnE,KAAK,CAACkD,GAAG,CAAC,kCAAkCc,OAAO,CAACF,IAAI,EAAE,CAAC;MACrFjC,eAAe,CAACsC,WAAW,CAACd,IAAI,CAAC;IACnC,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMO,eAAe,GAAGA,CAACG,IAAI,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC/C,MAAMC,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdL,IAAI;MACJC,IAAI;MACJK,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC;IAC3C,CAAC;IACDlC,gBAAgB,CAACmC,IAAI,IAAI,CAACN,YAAY,EAAE,GAAGM,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFzD,UAAU,CAAC,oBAAoB,CAAC;MAChC,MAAM0D,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,IAAI,CAAC,4BAA4B,CAAC;;MAE/D;MACA7D,OAAO,CAAC4D,QAAQ,CAAC1B,IAAI,CAACnC,IAAI,CAAC;MAC3BJ,YAAY,CAACiE,QAAQ,CAAC1B,IAAI,CAACxC,SAAS,CAAC;MACrCE,YAAY,CAACgE,QAAQ,CAAC1B,IAAI,CAAC9C,SAAS,CAAC;MACrCkB,kBAAkB,CAACsD,QAAQ,CAAC1B,IAAI,CAAC4B,gBAAgB,CAAC;;MAElD;MACA,MAAMC,oBAAoB,GAAGH,QAAQ,CAAC1B,IAAI,CAACxC,SAAS,CAACsE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAK9C,aAAa,CAAC8C,IAAI,CAAC;MAC7F,IAAIoB,oBAAoB,EAAE;QACxBjE,gBAAgB,CAACiE,oBAAoB,CAAC;;QAEtC;QACA,IAAI;UACF,MAAMf,WAAW,GAAG,MAAMnE,KAAK,CAACkD,GAAG,CAAC,kCAAkCgC,oBAAoB,CAACpB,IAAI,EAAE,CAAC;UAClGjC,eAAe,CAACsC,WAAW,CAACd,IAAI,CAAC;QACnC,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdP,OAAO,CAACO,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF;MAEArB,cAAc,CAAC,KAAK,CAAC;MACrBhB,UAAU,CAAC,EAAE,CAAC;MACd4C,eAAe,CAAC,QAAQc,QAAQ,CAAC1B,IAAI,CAACnC,IAAI,SAAS,EAAE,MAAM,CAAC;;MAE5D;MACAmE,YAAY,CAACH,oBAAoB,CAAC;IAEpC,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CrC,UAAU,CAAC,2CAA2C,CAAC;IACzD;EACF,CAAC;EAED,MAAMgE,YAAY,GAAIrB,OAAO,IAAK;IAChC,IAAI,CAACA,OAAO,EAAE;;IAEd;IACA,IAAIA,OAAO,CAAC3D,QAAQ,IAAID,cAAc,CAACC,QAAQ,IAC3C2D,OAAO,CAAC1D,QAAQ,IAAIF,cAAc,CAACE,QAAQ,IAC3C0D,OAAO,CAACzD,SAAS,CAAC+C,MAAM,IAAIlD,cAAc,CAACG,SAAS,EAAE;MACxDgB,YAAY,CAAC,SAAS,CAAC;MACvB;IACF;;IAEA;IACA,IAAIyC,OAAO,CAACvD,SAAS,IAAID,eAAe,CAACC,SAAS,IAC9CuD,OAAO,CAAC3D,QAAQ,IAAIG,eAAe,CAACH,QAAQ,IAC5C2D,OAAO,CAACtD,UAAU,IAAIF,eAAe,CAACE,UAAU,EAAE;MACpDa,YAAY,CAAC,QAAQ,CAAC;MACtB;IACF;EACF,CAAC;;EAED;EACA,MAAM+D,aAAa,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAClD,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,IAAI,CAAC,gCAAgC,EAAE;QAClEhB,OAAO,EAAEhD,aAAa,CAAC8C,IAAI;QAC3ByB,QAAQ;QACRE,UAAU,EAAED;MACd,CAAC,CAAC;MAEF,IAAIT,QAAQ,CAAC1B,IAAI,CAACqC,OAAO,EAAE;QACzBzB,eAAe,CAACc,QAAQ,CAAC1B,IAAI,CAACjC,OAAO,EAAE,SAAS,CAAC;QACjD;QACA,MAAMuE,UAAU,GAAG,MAAM3F,KAAK,CAACkD,GAAG,CAAC,iCAAiClC,aAAa,CAAC8C,IAAI,EAAE,CAAC;QACzF7C,gBAAgB,CAAC0E,UAAU,CAACtC,IAAI,CAAC;MACnC,CAAC,MAAM;QACLY,eAAe,CAACc,QAAQ,CAAC1B,IAAI,CAACK,KAAK,EAAE,OAAO,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCO,eAAe,CAAC,0BAA0B,EAAE,OAAO,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM2B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQ1D,SAAS;MACf,KAAK,UAAU;QACb,oBACE/B,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAA0F,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE5B9F,OAAA;YAAK+F,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,mBAAmB,EAAE,SAAS;cAAEC,GAAG,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBACjG1F,OAAA;cAAK+F,KAAK,EAAE;gBAAEK,UAAU,EAAE,SAAS;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAZ,QAAA,gBAC1E1F,OAAA;gBAAA0F,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAAC0F,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtF9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAAC4F,gBAAgB,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1F9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,CAACjF,aAAa,CAAC0F,cAAc,GAAG1F,aAAa,CAAC4F,gBAAgB,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrH9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,CAACjF,aAAa,CAAC6F,SAAS,GAAG,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAEN9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEK,UAAU,EAAE,SAAS;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAZ,QAAA,gBAC1E1F,OAAA;gBAAA0F,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAAC8F,SAAS,CAACC,cAAc,CAAC,CAAC,EAAC,SAAO;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpF9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACgG,SAAS,EAAC,QAAM;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClE9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACiG,kBAAkB,CAACN,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzF9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACkG,cAAc,CAACP,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,GAAI,gCAAe,EAAC7E,aAAa,CAACT,SAAS,CAAC+C,MAAM,EAAC,GAAC;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzD9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGtF,SAAS,CAAC4G,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKrG,aAAa,CAAC8C,IAAI,CAAC,CAACwD,GAAG,CAACC,QAAQ,iBACjEpH,OAAA;gBAEE+F,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrBC,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAE,SAAS;kBACjBC,MAAM,EAAE,CAAAzF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8B,IAAI,MAAKyD,QAAQ,CAACzD,IAAI,GAAG,mBAAmB,GAAG;gBAC3E,CAAE;gBACF4D,OAAO,EAAEA,CAAA,KAAMzF,mBAAmB,CAACsF,QAAQ,CAAE;gBAAA1B,QAAA,gBAE7C1F,OAAA;kBAAA0F,QAAA,EAAS0B,QAAQ,CAACzD;gBAAI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAChC9F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9F,OAAA;kBAAA0F,QAAA,GAAO,OAAK,EAAC0B,QAAQ,CAACI,WAAW,CAAChB,OAAO,CAAC,CAAC,CAAC,EAAC,UAAQ,EAACY,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC,CAACjB,cAAc,CAAC,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAZ7IsB,QAAQ,CAACzD,IAAI;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL3D,KAAK,iBACJnC,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAAZ,QAAA,gBAC1E1F,OAAA;cAAA0F,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEK,UAAU,EAAE,SAAS;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAZ,QAAA,gBAC1E1F,OAAA;gBAAA0F,QAAA,EAAKvD,KAAK,CAAC2F;cAAK;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB9F,OAAA;gBAAA0F,QAAA,EAAIvD,KAAK,CAAC4F;cAAW;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B9F,OAAA;gBAAG+F,KAAK,EAAE;kBAAEiC,KAAK,EAAE;gBAAU,CAAE;gBAAAtC,QAAA,gBAAC1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3D,KAAK,CAAC8F,MAAM;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3E9F,OAAA;gBACEuH,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACAzD,eAAe,CAAC,YAAY3B,KAAK,CAAC8F,MAAM,EAAE,EAAE,SAAS,CAAC;kBACtD7F,QAAQ,CAAC,IAAI,CAAC;gBAChB,CAAE;gBACF2D,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrB4B,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,UAAU;kBACnBC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAE;gBACV,CAAE;gBAAA3B,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,YAAY;QACf,oBACE9F,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAA0F,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGjC9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,EAAI;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGtF,SAAS,CAAC4G,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKrG,aAAa,CAAC8C,IAAI,CAAC,CAACwD,GAAG,CAACC,QAAQ,IAAI;gBACrE,MAAMc,QAAQ,GAAGd,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC;gBACnF,MAAMM,YAAY,GAAGf,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACQ,SAAS,EAAE,CAAC,CAAC,GAAGhB,QAAQ,CAACK,iBAAiB,CAACtE,MAAM;gBAChI,MAAMkF,YAAY,GAAGjB,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACU,SAAS,EAAE,CAAC,CAAC,GAAGlB,QAAQ,CAACK,iBAAiB,CAACtE,MAAM;gBAEhI,oBACEnD,OAAA;kBAEE+F,KAAK,EAAE;oBACLK,UAAU,EAAE,SAAS;oBACrBC,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAE,SAAS;oBACjBC,MAAM,EAAE,CAAAzF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8B,IAAI,MAAKyD,QAAQ,CAACzD,IAAI,GAAG,mBAAmB,GAAG;kBAC3E,CAAE;kBACF4D,OAAO,EAAEA,CAAA,KAAMzF,mBAAmB,CAACsF,QAAQ,CAAE;kBAAA1B,QAAA,gBAE7C1F,OAAA;oBAAI+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAY,CAAE;oBAAA7C,QAAA,EAAE0B,QAAQ,CAACzD;kBAAI;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxD9F,OAAA;oBAAG+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE,OAAO;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA9C,QAAA,gBAChD1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoC,QAAQ,CAACtB,cAAc,CAAC,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACJ9F,OAAA;oBAAG+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE,OAAO;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA9C,QAAA,gBAChD1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACqC,YAAY,CAAC3B,OAAO,CAAC,CAAC,CAAC,EAAC,KACvD;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJ9F,OAAA;oBAAG+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE,OAAO;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA9C,QAAA,gBAChD1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACsB,QAAQ,CAACqB,MAAM,CAACjC,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACJ9F,OAAA;oBAAK+F,KAAK,EAAE;sBACVK,UAAU,EAAE+B,YAAY,GAAG,CAAC,GAAG,SAAS,GAAGA,YAAY,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;sBACnFO,MAAM,EAAE,KAAK;sBACbpC,YAAY,EAAE,KAAK;sBACnBqC,SAAS,EAAE;oBACb;kBAAE;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA,GAzBAsB,QAAQ,CAACzD,IAAI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Bf,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLjE,gBAAgB,iBACf7B,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,GAAI,qBAAI,EAAC7D,gBAAgB,CAAC8B,IAAI,EAAC,uBAAqB;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEzD9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvG7D,gBAAgB,CAAC4F,iBAAiB,CAACN,GAAG,CAAC,CAACyB,QAAQ,EAAEC,KAAK,kBACtD7I,OAAA;gBAEE+F,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrBC,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBgB,MAAM,EAAE;gBACV,CAAE;gBAAA5B,QAAA,gBAEF1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAEwC,MAAM,EAAE,YAAY;oBAAEO,aAAa,EAAE;kBAAa,CAAE;kBAAApD,QAAA,EAC9DkD,QAAQ,CAACG,YAAY,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eAEL9F,OAAA;kBAAK+F,KAAK,EAAE;oBAAEyC,QAAQ,EAAE;kBAAS,CAAE;kBAAA9C,QAAA,gBACjC1F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACf,IAAI,CAACjB,cAAc,CAAC,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9D9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACK,OAAO;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnD9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACM,QAAQ;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrD9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACO,UAAU;kBAAA;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzD9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACQ,MAAM,CAAC5C,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAE5D9F,OAAA;oBAAK+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/B1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpF1F,OAAA;wBAAA0F,QAAA,EAAM;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvB9F,OAAA;wBAAM+F,KAAK,EAAE;0BAAEiC,KAAK,EAAEY,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGQ,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA1C,QAAA,GACzGkD,QAAQ,CAACR,SAAS,CAAC5B,OAAO,CAAC,CAAC,CAAC,EAAC,KACjC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACA1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAEwC,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGQ,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;0BAC/FM,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIX,QAAQ,CAACR,SAAS,GAAG,EAAE,GAAI,GAAG,GAAG;0BAC5CoB,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9F,OAAA;oBAAK+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/B1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpF1F,OAAA;wBAAA0F,QAAA,EAAM;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvB9F,OAAA;wBAAM+F,KAAK,EAAE;0BAAEiC,KAAK,EAAEY,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGM,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA5C,QAAA,GACzGkD,QAAQ,CAACN,SAAS,CAAC9B,OAAO,CAAC,CAAC,CAAC,EAAC,KACjC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACA1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAEwC,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGM,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;0BAC/FI,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIX,QAAQ,CAACN,SAAS,GAAG,EAAE,GAAI,GAAG,GAAG;0BAC5CkB,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9F,OAAA;oBAAK+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/B1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpF1F,OAAA;wBAAA0F,QAAA,EAAM;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3B9F,OAAA;wBAAM+F,KAAK,EAAE;0BAAEiC,KAAK,EAAE;wBAAU,CAAE;wBAAAtC,QAAA,GAC/BkD,QAAQ,CAACa,aAAa,CAACjD,OAAO,CAAC,CAAC,CAAC,EAAC,KACrC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACA1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIX,QAAQ,CAACa,aAAa,GAAG,EAAE,GAAI,GAAG,GAAG;0BAChDD,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GApFD+C,KAAK;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqFP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN9F,OAAA;cAAK+F,KAAK,EAAE;gBAAE4C,SAAS,EAAE;cAAO,CAAE;cAAAjD,QAAA,gBAChC1F,OAAA;gBAAA0F,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/B9F,OAAA;gBAAK+F,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,mBAAmB,EAAE,sCAAsC;kBAAEC,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,gBACxG1F,OAAA;kBACEuH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACtF,WAAW,EAAE;sBAChB6B,eAAe,CAAC,+BAA+B,EAAE,SAAS,CAAC;sBAC3D5B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFwH,QAAQ,EAAEzH,WAAY;kBACtB8D,KAAK,EAAE;oBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C+F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAyD,QAAA,GACH,+BACoB,eAAA1F,OAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzB9F,OAAA;oBAAA0F,QAAA,EAAO;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eAET9F,OAAA;kBACEuH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACtF,WAAW,EAAE;sBAChB6B,eAAe,CAAC,+BAA+B,EAAE,SAAS,CAAC;sBAC3D5B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFwH,QAAQ,EAAEzH,WAAY;kBACtB8D,KAAK,EAAE;oBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C+F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAyD,QAAA,GACH,mCACwB,eAAA1F,OAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7B9F,OAAA;oBAAA0F,QAAA,EAAO;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAET9F,OAAA;kBACEuH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACtF,WAAW,EAAE;sBAChB6B,eAAe,CAAC,8BAA8B,EAAE,SAAS,CAAC;sBAC1D5B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFwH,QAAQ,EAAEzH,WAAY;kBACtB8D,KAAK,EAAE;oBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C+F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAyD,QAAA,GACH,+BACoB,eAAA1F,OAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzB9F,OAAA;oBAAA0F,QAAA,EAAO;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAAZ,QAAA,gBAC1E1F,OAAA;cAAA0F,QAAA,EAAI;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEhC,CAAC,MAAM;cACN,MAAM6D,OAAO,GAAGvJ,SAAS,CACtB4G,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKrG,aAAa,CAAC8C,IAAI,CAAC,CAC3CiG,OAAO,CAAC3C,CAAC,IAAIA,CAAC,CAACQ,iBAAiB,CAAC;cAEpC,MAAMS,QAAQ,GAAGyB,OAAO,CAACjC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC;cAChE,MAAMgC,WAAW,GAAG,CAAC,CAAC;cACtB,MAAMC,aAAa,GAAG,CAAC,CAAC;cACxB,MAAMC,cAAc,GAAG,CAAC,CAAC;cAEzBJ,OAAO,CAACK,OAAO,CAACpC,GAAG,IAAI;gBACrBiC,WAAW,CAACjC,GAAG,CAACmB,YAAY,CAAC,GAAG,CAACc,WAAW,CAACjC,GAAG,CAACmB,YAAY,CAAC,IAAI,CAAC,IAAInB,GAAG,CAACC,IAAI;gBAC/EiC,aAAa,CAAClC,GAAG,CAACqB,OAAO,CAAC,GAAG,CAACa,aAAa,CAAClC,GAAG,CAACqB,OAAO,CAAC,IAAI,CAAC,IAAIrB,GAAG,CAACC,IAAI;gBACzEkC,cAAc,CAACnC,GAAG,CAACsB,QAAQ,CAAC,GAAG,CAACa,cAAc,CAACnC,GAAG,CAACsB,QAAQ,CAAC,IAAI,CAAC,IAAItB,GAAG,CAACC,IAAI;cAC/E,CAAC,CAAC;cAEF,oBACE7H,OAAA;gBAAK+F,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,mBAAmB,EAAE,sCAAsC;kBAAEC,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,gBACxG1F,OAAA;kBAAA0F,QAAA,gBACE1F,OAAA;oBAAA0F,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzBzC,MAAM,CAAC4G,OAAO,CAACJ,WAAW,CAAC,CAAC1C,GAAG,CAAC,CAAC,CAAC+C,SAAS,EAAEC,KAAK,CAAC,kBAClDnK,OAAA;oBAAqB+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,gBAC9C1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE;sBAAgB,CAAE;sBAAA3D,QAAA,gBAC/D1F,OAAA;wBAAM+F,KAAK,EAAE;0BAAE+C,aAAa,EAAE;wBAAa,CAAE;wBAAApD,QAAA,EAAEwE,SAAS,CAAClB,OAAO,CAAC,GAAG,EAAE,GAAG;sBAAC;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClF9F,OAAA;wBAAA0F,QAAA,GAAO,CAAEyE,KAAK,GAAGjC,QAAQ,GAAI,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEK,UAAU,EAAE,MAAM;wBAAEsC,MAAM,EAAE,KAAK;wBAAEpC,YAAY,EAAE,KAAK;wBAAEgD,QAAQ,EAAE;sBAAS,CAAE;sBAAA5D,QAAA,eACzF1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIY,KAAK,GAAGjC,QAAQ,GAAI,GAAG;wBACpC;sBAAE;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAXEoE,SAAS;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYd,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN9F,OAAA;kBAAA0F,QAAA,gBACE1F,OAAA;oBAAA0F,QAAA,EAAI;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACpBzC,MAAM,CAAC4G,OAAO,CAACH,aAAa,CAAC,CAAC3C,GAAG,CAAC,CAAC,CAAC8B,OAAO,EAAEkB,KAAK,CAAC,kBAClDnK,OAAA;oBAAmB+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,gBAC5C1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE;sBAAgB,CAAE;sBAAA3D,QAAA,gBAC/D1F,OAAA;wBAAM+F,KAAK,EAAE;0BAAE+C,aAAa,EAAE;wBAAa,CAAE;wBAAApD,QAAA,EAAEuD;sBAAO;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC9D9F,OAAA;wBAAA0F,QAAA,GAAO,CAAEyE,KAAK,GAAGjC,QAAQ,GAAI,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEK,UAAU,EAAE,MAAM;wBAAEsC,MAAM,EAAE,KAAK;wBAAEpC,YAAY,EAAE,KAAK;wBAAEgD,QAAQ,EAAE;sBAAS,CAAE;sBAAA5D,QAAA,eACzF1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIY,KAAK,GAAGjC,QAAQ,GAAI,GAAG;wBACpC;sBAAE;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAXEmD,OAAO;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYZ,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN9F,OAAA;kBAAA0F,QAAA,gBACE1F,OAAA;oBAAA0F,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnBzC,MAAM,CAAC4G,OAAO,CAACF,cAAc,CAAC,CAAC5C,GAAG,CAAC,CAAC,CAAC+B,QAAQ,EAAEiB,KAAK,CAAC,kBACpDnK,OAAA;oBAAoB+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,gBAC7C1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE;sBAAgB,CAAE;sBAAA3D,QAAA,gBAC/D1F,OAAA;wBAAM+F,KAAK,EAAE;0BAAE+C,aAAa,EAAE;wBAAa,CAAE;wBAAApD,QAAA,EAAEwD;sBAAQ;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/D9F,OAAA;wBAAA0F,QAAA,GAAO,CAAEyE,KAAK,GAAGjC,QAAQ,GAAI,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEK,UAAU,EAAE,MAAM;wBAAEsC,MAAM,EAAE,KAAK;wBAAEpC,YAAY,EAAE,KAAK;wBAAEgD,QAAQ,EAAE;sBAAS,CAAE;sBAAA5D,QAAA,eACzF1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIY,KAAK,GAAGjC,QAAQ,GAAI,GAAG;wBACpC;sBAAE;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAXEoD,QAAQ;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYb,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEV,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,SAAS;QACZ,oBACE9F,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAA0F,QAAA,EAAI;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE/B9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,SAAS;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBAC3E1F,OAAA;gBAAA0F,QAAA,gBACE1F,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACX,QAAQ,CAAC0G,cAAc,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChF9F,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,MAAE,EAACjF,aAAa,CAAC0F,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACvF9F,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,MAAE,EAACjF,aAAa,CAAC4F,gBAAgB,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC3F9F,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAACjF,aAAa,CAAC0F,cAAc,GAAG1F,aAAa,CAAC4F,gBAAgB,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC,eACN9F,OAAA;gBAAA0F,QAAA,gBACE1F,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAACjF,aAAa,CAACuJ,gBAAgB,GAAG,GAAG,EAAE5D,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC9F9F,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAACjF,aAAa,CAAC6F,SAAS,GAAG,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGrC,MAAM,CAAC4G,OAAO,CAAC5I,eAAe,CAAC,CAAC8F,GAAG,CAAC,CAAC,CAACxD,IAAI,EAAE0G,QAAQ,CAAC,kBACpDrK,OAAA;gBAAgB+F,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE,KAAK;kBAAEgE,SAAS,EAAE;gBAAS,CAAE;gBAAA5E,QAAA,gBAC1G1F,OAAA;kBAAA0F,QAAA,EAAS/B,IAAI,CAACqF,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACzC9F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9F,OAAA;kBAAM+F,KAAK,EAAE;oBAAEiC,KAAK,EAAE;kBAAU,CAAE;kBAAAtC,QAAA,GAAE2E,QAAQ,CAACE,aAAa,CAAC/D,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClF9F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN9F,OAAA;kBAAO+F,KAAK,EAAE;oBAAEyE,OAAO,EAAE;kBAAI,CAAE;kBAAA9E,QAAA,EAC5B2E,QAAQ,CAACE,aAAa,GAAGF,QAAQ,CAACI,UAAU,GAAG,IAAI,GAAG;gBAAI;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA,GAPAnC,IAAI;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQT,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAAZ,QAAA,gBAC1E1F,OAAA;cAAA0F,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5B9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBACxG1F,OAAA;gBACEuH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI,CAACtF,WAAW,EAAE;oBAChB6B,eAAe,CAAC,wCAAwC,EAAE,SAAS,CAAC;oBACpE5B,cAAc,CAAC,IAAI,CAAC;kBACtB;gBACF,CAAE;gBACFwH,QAAQ,EAAEzH,WAAY;gBACtB8D,KAAK,EAAE;kBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;kBAC5C+F,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;gBACxC,CAAE;gBAAAyD,QAAA,GACH,yCACyB,eAAA1F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9B9F,OAAA;kBAAA0F,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAET9F,OAAA;gBACEuH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI,CAACtF,WAAW,EAAE;oBAChB6B,eAAe,CAAC,6BAA6B,EAAE,SAAS,CAAC;oBACzD5B,cAAc,CAAC,IAAI,CAAC;kBACtB;gBACF,CAAE;gBACFwH,QAAQ,EAAEzH,WAAY;gBACtB8D,KAAK,EAAE;kBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;kBAC5C+F,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;gBACxC,CAAE;gBAAAyD,QAAA,GACH,4BACiB,eAAA1F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtB9F,OAAA;kBAAA0F,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAET9F,OAAA;gBACEuH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI,CAACtF,WAAW,EAAE;oBAChB6B,eAAe,CAAC,yBAAyB,EAAE,SAAS,CAAC;oBACrD5B,cAAc,CAAC,IAAI,CAAC;kBACtB;gBACF,CAAE;gBACFwH,QAAQ,EAAEzH,WAAY;gBACtB8D,KAAK,EAAE;kBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;kBAC5C+F,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;gBACxC,CAAE;gBAAAyD,QAAA,GACH,yBACc,eAAA1F,OAAA;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnB9F,OAAA;kBAAA0F,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,UAAU;QACb,oBACE9F,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAA0F,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAG5B9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBACxG1F,OAAA;gBAAK+F,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE,KAAK;kBAAEgE,SAAS,EAAE;gBAAS,CAAE;gBAAA5E,QAAA,gBAC/F1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAEwC,MAAM,EAAE;kBAAa,CAAE;kBAAA7C,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvD9F,OAAA;kBAAK+F,KAAK,EAAE;oBAAEyC,QAAQ,EAAE,QAAQ;oBAAEkC,UAAU,EAAE,MAAM;oBAAE1C,KAAK,EAAE;kBAAU,CAAE;kBAAAtC,QAAA,EACtEjE,YAAY,CAACkJ,KAAK,GAAGlJ,YAAY,CAACkJ,KAAK,CAAC3D,MAAM,CAAC4D,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,CAACC,QAAQ,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC,CAACpD,MAAM,CAAC,CAACC,GAAG,EAAEiD,CAAC,KAAKjD,GAAG,GAAGiD,CAAC,CAAC/C,IAAI,EAAE,CAAC,CAAC,CAACjB,cAAc,CAAC,CAAC,GAAG;gBAAG;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChK,CAAC,eACN9F,OAAA;kBAAA0F,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAEN9F,OAAA;gBAAK+F,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE,KAAK;kBAAEgE,SAAS,EAAE;gBAAS,CAAE;gBAAA5E,QAAA,gBAC/F1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAEwC,MAAM,EAAE;kBAAa,CAAE;kBAAA7C,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtD9F,OAAA;kBAAK+F,KAAK,EAAE;oBAAEyC,QAAQ,EAAE,QAAQ;oBAAEkC,UAAU,EAAE,MAAM;oBAAE1C,KAAK,EAAE;kBAAU,CAAE;kBAAAtC,QAAA,EACtEjE,YAAY,CAACkJ,KAAK,GAAGlJ,YAAY,CAACkJ,KAAK,CAAC3D,MAAM,CAAC4D,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAACC,QAAQ,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC,CAAC3H,MAAM,GAAG;gBAAG;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC,eACN9F,OAAA;kBAAA0F,QAAA,EAAO;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eAEN9F,OAAA;gBAAK+F,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE,KAAK;kBAAEgE,SAAS,EAAE;gBAAS,CAAE;gBAAA5E,QAAA,gBAC/F1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAEwC,MAAM,EAAE;kBAAa,CAAE;kBAAA7C,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChE9F,OAAA;kBAAK+F,KAAK,EAAE;oBAAEyC,QAAQ,EAAE,QAAQ;oBAAEkC,UAAU,EAAE,MAAM;oBAAE1C,KAAK,EAAE;kBAAU,CAAE;kBAAAtC,QAAA,EACtEjE,YAAY,CAACqF,kBAAkB,GAAGrF,YAAY,CAACqF,kBAAkB,CAACN,OAAO,CAAC,CAAC,CAAC,GAAG;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACN9F,OAAA;kBAAA0F,QAAA,EAAO;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eAEN9F,OAAA;gBAAK+F,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE,KAAK;kBAAEgE,SAAS,EAAE;gBAAS,CAAE;gBAAA5E,QAAA,gBAC/F1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAEwC,MAAM,EAAE;kBAAa,CAAE;kBAAA7C,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9D9F,OAAA;kBAAK+F,KAAK,EAAE;oBAAEyC,QAAQ,EAAE,QAAQ;oBAAEkC,UAAU,EAAE,MAAM;oBAAE1C,KAAK,EAAE;kBAAU,CAAE;kBAAAtC,QAAA,EACtEjE,YAAY,CAACsJ,iBAAiB,GAAGtJ,YAAY,CAACsJ,iBAAiB,CAACvE,OAAO,CAAC,CAAC,CAAC,GAAG;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACN9F,OAAA;kBAAA0F,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEzBrE,YAAY,CAACkJ,KAAK,IAAIlJ,YAAY,CAACkJ,KAAK,CAACxH,MAAM,GAAG,CAAC,gBAClDnD,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGjE,YAAY,CAACkJ,KAAK,CAACxD,GAAG,CAAC,CAAC6D,IAAI,EAAEnC,KAAK,kBAClC7I,OAAA;gBAEE+F,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrBC,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBgB,MAAM,EAAE;gBACV,CAAE;gBAAA5B,QAAA,gBAEF1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAEwC,MAAM,EAAE,YAAY;oBAAEvC,OAAO,EAAE,MAAM;oBAAEiF,UAAU,EAAE;kBAAS,CAAE;kBAAAvF,QAAA,GACxEsF,IAAI,CAACF,SAAS,KAAK,UAAU,IAAI,IAAI,EACrCE,IAAI,CAACF,SAAS,KAAK,SAAS,IAAI,IAAI,EACpCE,IAAI,CAACF,SAAS,KAAK,WAAW,IAAI,IAAI,EACtCE,IAAI,CAACF,SAAS,KAAK,SAAS,IAAI,GAAG,EACnCE,IAAI,CAACF,SAAS,KAAK,cAAc,IAAI,IAAI,eAC1C9K,OAAA;oBAAM+F,KAAK,EAAE;sBAAEmF,UAAU,EAAE;oBAAM,CAAE;oBAAAxF,QAAA,EAAEsF,IAAI,CAACrH;kBAAI;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eAEL9F,OAAA;kBAAK+F,KAAK,EAAE;oBAAEyC,QAAQ,EAAE;kBAAS,CAAE;kBAAA9C,QAAA,gBACjC1F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACkF,IAAI,CAACF,SAAS,CAAC9B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;kBAAA;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChE9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACkF,IAAI,CAACnD,IAAI,CAACjB,cAAc,CAAC,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1D9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACkF,IAAI,CAACG,QAAQ;kBAAA;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjD9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACkF,IAAI,CAACI,gBAAgB,CAAC5E,OAAO,CAAC,CAAC,CAAC,EAAC,aAAW;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAElF9F,OAAA;oBAAK+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/B1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpF1F,OAAA;wBAAA0F,QAAA,EAAM;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtB9F,OAAA;wBAAM+F,KAAK,EAAE;0BAAEiC,KAAK,EAAEgD,IAAI,CAACK,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAGL,IAAI,CAACK,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA3F,QAAA,GACjGsF,IAAI,CAACK,QAAQ,CAAC7E,OAAO,CAAC,CAAC,CAAC,EAAC,GAC5B;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACA1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAE4E,IAAI,CAACK,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAGL,IAAI,CAACK,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS;0BACvF3C,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAGyB,IAAI,CAACK,QAAQ,GAAG;0BAC1B7B,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9F,OAAA;oBAAK+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/B1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpF1F,OAAA;wBAAA0F,QAAA,EAAM;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpB9F,OAAA;wBAAM+F,KAAK,EAAE;0BAAEiC,KAAK,EAAEgD,IAAI,CAACM,MAAM,GAAG,EAAE,GAAG,SAAS,GAAGN,IAAI,CAACM,MAAM,GAAG,EAAE,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA5F,QAAA,GAC7FsF,IAAI,CAACM,MAAM,CAAC9E,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1B;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACA1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAE4E,IAAI,CAACM,MAAM,GAAG,EAAE,GAAG,SAAS,GAAGN,IAAI,CAACM,MAAM,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS;0BACnF5C,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAGyB,IAAI,CAACM,MAAM,GAAG;0BACxB9B,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9F,OAAA;oBAAK+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/B1F,OAAA;sBAAK+F,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpF1F,OAAA;wBAAA0F,QAAA,EAAM;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxB9F,OAAA;wBAAM+F,KAAK,EAAE;0BAAEiC,KAAK,EAAE;wBAAU,CAAE;wBAAAtC,QAAA,GAC/BsF,IAAI,CAACO,UAAU,CAAC/E,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9B;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9F,OAAA;sBAAK+F,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACA1F,OAAA;wBAAK+F,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAGyB,IAAI,CAACO,UAAU,GAAG;0BAC5B/B,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAxFD+C,KAAK;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyFP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAEN9F,OAAA;cAAG+F,KAAK,EAAE;gBAAEuE,SAAS,EAAE,QAAQ;gBAAEtC,KAAK,EAAE,MAAM;gBAAE3B,OAAO,EAAE;cAAO,CAAE;cAAAX,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAAZ,QAAA,gBAC1E1F,OAAA;cAAA0F,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE5B9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGrC,MAAM,CAAC4G,OAAO,CAACtI,SAAS,CAAC,CAACwF,GAAG,CAAC,CAAC,CAACqE,QAAQ,EAAEC,KAAK,CAAC,kBAC/CzL,OAAA;gBAEE+F,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrBC,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBgB,MAAM,EAAE;gBACV,CAAE;gBAAA5B,QAAA,gBAEF1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAEwC,MAAM,EAAE,YAAY;oBAAEO,aAAa,EAAE,YAAY;oBAAE9C,OAAO,EAAE,MAAM;oBAAEiF,UAAU,EAAE;kBAAS,CAAE;kBAAAvF,QAAA,GACrG8F,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,SAAS,IAAI,IAAI,EAC9BA,QAAQ,KAAK,WAAW,IAAI,IAAI,EAChCA,QAAQ,KAAK,SAAS,IAAI,GAAG,EAC7BA,QAAQ,KAAK,cAAc,IAAI,IAAI,eACpCxL,OAAA;oBAAM+F,KAAK,EAAE;sBAAEmF,UAAU,EAAE;oBAAM,CAAE;oBAAAxF,QAAA,EAAE8F,QAAQ,CAACxC,OAAO,CAAC,GAAG,EAAE,GAAG;kBAAC;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eAEL9F,OAAA;kBAAK+F,KAAK,EAAE;oBAAEyC,QAAQ,EAAE,QAAQ;oBAAErC,YAAY,EAAE;kBAAO,CAAE;kBAAAT,QAAA,gBACvD1F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC2F,KAAK,CAACC,IAAI,EAAC,OAAK;kBAAA;oBAAA/F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/C9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC2F,KAAK,CAACE,WAAW,EAAC,aAAW;kBAAA;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACnE9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC2F,KAAK,CAACJ,QAAQ;kBAAA;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClD9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC2F,KAAK,CAACG,KAAK;kBAAA;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eAEN9F,OAAA;kBACEuH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACtF,WAAW,IAAIpB,aAAa,CAACT,SAAS,CAAC+C,MAAM,GAAG,CAAC,EAAE;sBACtD,MAAM0I,OAAO,GAAGhL,aAAa,CAACT,SAAS,CAAC,CAAC,CAAC;sBAC1C0L,WAAW,CAACN,QAAQ,EAAEK,OAAO,EAAE,IAAI,CAAC;oBACtC;kBACF,CAAE;kBACFnC,QAAQ,EAAEzH,WAAW,IAAIpB,aAAa,CAACX,QAAQ,GAAGuL,KAAK,CAACC,IAAK;kBAC7D3F,KAAK,EAAE;oBACLK,UAAU,EAAGnE,WAAW,IAAIpB,aAAa,CAACX,QAAQ,GAAGuL,KAAK,CAACC,IAAI,GAAI,MAAM,GAAG,SAAS;oBACrF1D,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,WAAW;oBACpBC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAGpF,WAAW,IAAIpB,aAAa,CAACX,QAAQ,GAAGuL,KAAK,CAACC,IAAI,GAAI,aAAa,GAAG,SAAS;oBACxFnC,KAAK,EAAE,MAAM;oBACbf,QAAQ,EAAE;kBACZ,CAAE;kBAAA9C,QAAA,EAED7E,aAAa,CAACX,QAAQ,GAAGuL,KAAK,CAACC,IAAI,GAAG,oBAAoB,GAC1DzJ,WAAW,GAAG,cAAc,GAAG,WAAWuJ,QAAQ,CAACxC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC;cAAA,GA7CJ0F,QAAQ;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8CV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9F,OAAA;cAAK+F,KAAK,EAAE;gBAAE4C,SAAS,EAAE,MAAM;gBAAEtC,OAAO,EAAE,MAAM;gBAAED,UAAU,EAAE,SAAS;gBAAEE,YAAY,EAAE;cAAM,CAAE;cAAAZ,QAAA,gBAC7F1F,OAAA;gBAAA0F,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3B9F,OAAA;gBAAK+F,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,mBAAmB,EAAE,sCAAsC;kBAAEC,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,gBACxG1F,OAAA;kBACEuH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACtF,WAAW,EAAE;sBAChB6B,eAAe,CAAC,uCAAuC,EAAE,SAAS,CAAC;sBACnE5B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFwH,QAAQ,EAAEzH,WAAY;kBACtB8D,KAAK,EAAE;oBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C+F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAyD,QAAA,GACH,gCACqB,eAAA1F,OAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1B9F,OAAA;oBAAA0F,QAAA,EAAO;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eAET9F,OAAA;kBACEuH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACtF,WAAW,EAAE;sBAChB6B,eAAe,CAAC,gCAAgC,EAAE,SAAS,CAAC;sBAC5D5B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFwH,QAAQ,EAAEzH,WAAY;kBACtB8D,KAAK,EAAE;oBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C+F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAyD,QAAA,GACH,8BACmB,eAAA1F,OAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxB9F,OAAA;oBAAA0F,QAAA,EAAO;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAET9F,OAAA;kBACEuH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACtF,WAAW,EAAE;sBAChB6B,eAAe,CAAC,4BAA4B,EAAE,SAAS,CAAC;sBACxD5B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFwH,QAAQ,EAAEzH,WAAY;kBACtB8D,KAAK,EAAE;oBACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C+F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAyD,QAAA,GACH,mCACwB,eAAA1F,OAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7B9F,OAAA;oBAAA0F,QAAA,EAAO;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,YAAY;QACf,oBACE9F,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAA0F,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEjC9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGrC,MAAM,CAAC4G,OAAO,CAACpJ,aAAa,CAACkL,eAAe,CAAC,CAAC5E,GAAG,CAAC,CAAC,CAAC/B,QAAQ,EAAE4G,MAAM,CAAC,kBACpEhM,OAAA;gBAAoB+F,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,gBACzF1F,OAAA;kBAAI+F,KAAK,EAAE;oBAAE+C,aAAa,EAAE,YAAY;oBAAEP,MAAM,EAAE;kBAAa,CAAE;kBAAA7C,QAAA,GAC9DN,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,QAAQ,IAAI,IAAI,EAC7BA,QAAQ,KAAK,gBAAgB,IAAI,KAAK,EACtC,GAAG,GAAGA,QAAQ;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACL9F,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACkG,MAAM,CAACxF,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAClDjF,aAAa,CAACoL,gBAAgB,CAAC7G,QAAQ,CAAC,iBACvCpF,OAAA;kBAAA0F,QAAA,gBAAG1F,OAAA;oBAAA0F,QAAA,EAAQ;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACoL,gBAAgB,CAAC7G,QAAQ,CAAC;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC/E;cAAA,GAXOV,QAAQ;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA;YAAK+F,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChG1F,OAAA;cAAA0F,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9F,OAAA;cAAK+F,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGrC,MAAM,CAAC4G,OAAO,CAAC1I,YAAY,CAAC,CAAC4F,GAAG,CAAC,CAAC,CAACxD,IAAI,EAAEuI,IAAI,CAAC,KAAK;gBAClD,MAAMC,YAAY,GAAGtL,aAAa,CAACU,YAAY,CAACsJ,QAAQ,CAAClH,IAAI,CAAC;gBAC9D,MAAMyI,WAAW,GAAGF,IAAI,CAACG,aAAa,CAACC,KAAK,CAACC,MAAM,IAAI1L,aAAa,CAACU,YAAY,CAACsJ,QAAQ,CAAC0B,MAAM,CAAC,CAAC;gBACnG,MAAMC,sBAAsB,GAAGnJ,MAAM,CAACoJ,MAAM,CAAC5L,aAAa,CAACoL,gBAAgB,CAAC,CAACpB,QAAQ,CAAClH,IAAI,CAAC;gBAE3F,oBACE3D,OAAA;kBAEE+F,KAAK,EAAE;oBACLK,UAAU,EAAE+F,YAAY,GAAG,SAAS,GAAG,SAAS;oBAChD9F,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBgB,MAAM,EAAEkF,sBAAsB,GAAG,mBAAmB,GAAG,gBAAgB;oBACvEhC,OAAO,EAAE4B,WAAW,IAAID,YAAY,GAAG,CAAC,GAAG;kBAC7C,CAAE;kBAAAzG,QAAA,gBAEF1F,OAAA;oBAAI+F,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAa,CAAE;oBAAA7C,QAAA,EAAE/B;kBAAI;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChD9F,OAAA;oBAAG+F,KAAK,EAAE;sBAAEyC,QAAQ,EAAE,QAAQ;sBAAED,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,EAAEwG,IAAI,CAACnE;kBAAW;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzE9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoG,IAAI,CAAC9G,QAAQ;kBAAA;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjD9F,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoG,IAAI,CAACR,IAAI,EAAC,SAAO;kBAAA;oBAAA/F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,EAE/CoG,IAAI,CAACG,aAAa,CAAClJ,MAAM,GAAG,CAAC,iBAC5BnD,OAAA;oBAAA0F,QAAA,gBAAG1F,OAAA;sBAAA0F,QAAA,EAAQ;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoG,IAAI,CAACG,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC;kBAAA;oBAAA/G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CACtE,EAEAqG,YAAY,iBACXnM,OAAA;oBAAM+F,KAAK,EAAE;sBAAEiC,KAAK,EAAE,SAAS;sBAAE0C,UAAU,EAAE;oBAAO,CAAE;oBAAAhF,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC1E,EAEA,CAACqG,YAAY,IAAIC,WAAW,IAAI,CAACI,sBAAsB,iBACtDxM,OAAA;oBACEuH,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAAC+G,IAAI,CAAC9G,QAAQ,EAAEzB,IAAI,CAAE;oBAClDoC,KAAK,EAAE;sBACLK,UAAU,EAAE,SAAS;sBACrB4B,KAAK,EAAE,MAAM;sBACbV,MAAM,EAAE,MAAM;sBACdjB,OAAO,EAAE,UAAU;sBACnBC,YAAY,EAAE,KAAK;sBACnBe,MAAM,EAAE,SAAS;sBACjBsB,SAAS,EAAE;oBACb,CAAE;oBAAAjD,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEA0G,sBAAsB,iBACrBxM,OAAA;oBAAM+F,KAAK,EAAE;sBAAEiC,KAAK,EAAE,SAAS;sBAAE0C,UAAU,EAAE;oBAAO,CAAE;oBAAAhF,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC/E;gBAAA,GAzCInC,IAAI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0CN,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBACE9F,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAA0F,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB9F,OAAA;YAAA0F,QAAA,EAAG;UAA0E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;IAEZ;EACF,CAAC;;EAED;EACA,MAAMgG,WAAW,GAAG,MAAAA,CAAON,QAAQ,EAAEpE,QAAQ,EAAES,IAAI,GAAG,IAAI,KAAK;IAC7D,IAAI;MACF,MAAMjD,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,IAAI,CAAC,wCAAwC,EAAE;QAC1EhB,OAAO,EAAEhD,aAAa,CAAC8C,IAAI;QAC3BmH,SAAS,EAAEU,QAAQ;QACnBpE,QAAQ,EAAEA,QAAQ;QAClBS,IAAI,EAAEA;MACR,CAAC,CAAC;MAEF,IAAIjD,QAAQ,CAAC1B,IAAI,CAACqC,OAAO,EAAE;QACzBzB,eAAe,CAACc,QAAQ,CAAC1B,IAAI,CAACjC,OAAO,EAAE,SAAS,CAAC;;QAEjD;QACA,MAAM,CAAC+C,WAAW,EAAEwB,UAAU,CAAC,GAAG,MAAM3C,OAAO,CAACC,GAAG,CAAC,CAClDjD,KAAK,CAACkD,GAAG,CAAC,kCAAkClC,aAAa,CAAC8C,IAAI,EAAE,CAAC,EACjE9D,KAAK,CAACkD,GAAG,CAAC,iCAAiClC,aAAa,CAAC8C,IAAI,EAAE,CAAC,CACjE,CAAC;QAEFjC,eAAe,CAACsC,WAAW,CAACd,IAAI,CAAC;QACjCpC,gBAAgB,CAAC0E,UAAU,CAACtC,IAAI,CAAC;QACjChB,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACL4B,eAAe,CAACc,QAAQ,CAAC1B,IAAI,CAACK,KAAK,EAAE,OAAO,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CO,eAAe,CAAC,wBAAwB,EAAE,OAAO,CAAC;IACpD;EACF,CAAC;;EAED;EACA,IAAI3C,SAAS,KAAK,SAAS,EAAE;IAC3B,oBACEnB,OAAA;MAAK+F,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEqD,cAAc,EAAE,QAAQ;QAAE4B,UAAU,EAAE,QAAQ;QAAEvC,MAAM,EAAE,OAAO;QAAEtC,UAAU,EAAE;MAAU,CAAE;MAAAV,QAAA,eACtH1F,OAAA;QAAK+F,KAAK,EAAE;UAAEuE,SAAS,EAAE,QAAQ;UAAEtC,KAAK,EAAE;QAAO,CAAE;QAAAtC,QAAA,gBACjD1F,OAAA;UAAA0F,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjC9F,OAAA;UAAA0F,QAAA,EAAG;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtC9F,OAAA;UAAK+F,KAAK,EAAE;YAAEwC,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI3E,SAAS,KAAK,mBAAmB,EAAE;IACrC,oBACEnB,OAAA;MAAK+F,KAAK,EAAE;QAAEK,UAAU,EAAE,SAAS;QAAEuG,SAAS,EAAE,OAAO;QAAE3E,KAAK,EAAE;MAAO,CAAE;MAAAtC,QAAA,gBACvE1F,OAAA;QAAK+F,KAAK,EAAE;UAAEuE,SAAS,EAAE,QAAQ;UAAEjE,OAAO,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACnD1F,OAAA;UAAA0F,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/B9F,OAAA;UAAA0F,QAAA,EAAG;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAEN9F,OAAA,CAACF,QAAQ;QACP8M,eAAe,EAAEhJ,aAAc;QAC/BiJ,eAAe,EAAE,IAAK;QACtBnM,SAAS,EAAEA;MAAU;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEF9F,OAAA;QAAK+F,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEyG,QAAQ,EAAE,QAAQ;UAAEvE,MAAM,EAAE;QAAS,CAAE;QAAA7C,QAAA,gBACpE1F,OAAA;UAAA0F,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1B9F,OAAA;UAAK+F,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,EACvGhF,SAAS,CAACgE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACyC,GAAG,CAACtD,OAAO,iBAChC7D,OAAA;YAEE+F,KAAK,EAAE;cACLK,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE,MAAM;cACpBe,MAAM,EAAE,SAAS;cACjBC,MAAM,EAAE,uBAAuB;cAC/BkC,UAAU,EAAE;YACd,CAAE;YACFuD,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClH,KAAK,CAACmH,WAAW,GAAG,SAAU;YAC5DC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClH,KAAK,CAACmH,WAAW,GAAG,aAAc;YAChE3F,OAAO,EAAEA,CAAA,KAAM3D,aAAa,CAACC,OAAO,CAAE;YAAA6B,QAAA,gBAEtC1F,OAAA;cAAA0F,QAAA,EAAK7B,OAAO,CAACF;YAAI;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvB9F,OAAA;cAAA0F,QAAA,gBAAG1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjC,OAAO,CAACE,UAAU;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD9F,OAAA;cAAA0F,QAAA,gBAAG1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjC,OAAO,CAACuJ,eAAe,CAACpE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;YAAA;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E9F,OAAA;cAAA0F,QAAA,gBAAG1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjC,OAAO,CAAC3D,QAAQ,CAAC0G,cAAc,CAAC,CAAC,EAAC,OAAK;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1E9F,OAAA;cAAA0F,QAAA,gBAAG1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjC,OAAO,CAACzD,SAAS,CAAC+C,MAAM;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D9F,OAAA;cAAA0F,QAAA,gBAAG1F,OAAA;gBAAA0F,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjC,OAAO,CAAC1D,QAAQ;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAlB/CjC,OAAO,CAACF,IAAI;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBd,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI3E,SAAS,KAAK,SAAS,EAAE;IAC3B,oBACEnB,OAAA;MAAK+F,KAAK,EAAE;QACVK,UAAU,EAAE,mDAAmD;QAC/DuG,SAAS,EAAE,OAAO;QAClB3G,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,QAAQ;QACxB4B,UAAU,EAAE,QAAQ;QACpBjD,KAAK,EAAE,MAAM;QACbsC,SAAS,EAAE;MACb,CAAE;MAAA5E,QAAA,eACA1F,OAAA;QAAA0F,QAAA,gBACE1F,OAAA;UAAI+F,KAAK,EAAE;YAAEyC,QAAQ,EAAE,MAAM;YAAErC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E9F,OAAA;UAAA0F,QAAA,GAAI,MAAI,EAAC7E,aAAa,CAAC8C,IAAI,EAAC,cAAY;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C9F,OAAA;UAAG+F,KAAK,EAAE;YAAEyC,QAAQ,EAAE,QAAQ;YAAED,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,GAAC,yBAC3B,EAAC7E,aAAa,CAACkD,UAAU,EAAC,uCACnD;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ9F,OAAA;UAAK+F,KAAK,EAAE;YAAEK,UAAU,EAAE,iBAAiB;YAAEC,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,MAAM;YAAEiC,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,gBACrG1F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACX,QAAQ,CAAC0G,cAAc,CAAC,CAAC,EAAC,OAAK;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtF9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACV,QAAQ;UAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACT,SAAS,CAAC+C,MAAM;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9E9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/E,IAAI;UAAA;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACN9F,OAAA;UACEuH,OAAO,EAAEA,CAAA,KAAM8F,MAAM,CAAClC,QAAQ,CAACmC,MAAM,CAAC,CAAE;UACxCvH,KAAK,EAAE;YACLM,OAAO,EAAE,WAAW;YACpBmC,QAAQ,EAAE,QAAQ;YAClBpC,UAAU,EAAE,SAAS;YACrB4B,KAAK,EAAE,MAAM;YACbV,MAAM,EAAE,MAAM;YACdhB,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE;UACV,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI3E,SAAS,KAAK,QAAQ,EAAE;IAC1B,oBACEnB,OAAA;MAAK+F,KAAK,EAAE;QACVK,UAAU,EAAE,mDAAmD;QAC/DuG,SAAS,EAAE,OAAO;QAClB3G,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,QAAQ;QACxB4B,UAAU,EAAE,QAAQ;QACpBjD,KAAK,EAAE,MAAM;QACbsC,SAAS,EAAE;MACb,CAAE;MAAA5E,QAAA,eACA1F,OAAA;QAAA0F,QAAA,gBACE1F,OAAA;UAAI+F,KAAK,EAAE;YAAEyC,QAAQ,EAAE,MAAM;YAAErC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxE9F,OAAA;UAAA0F,QAAA,GAAI,cAAY,EAAC7E,aAAa,CAAC8C,IAAI;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzC9F,OAAA;UAAG+F,KAAK,EAAE;YAAEyC,QAAQ,EAAE,QAAQ;YAAED,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,GAAC,eACrC,EAAC7E,aAAa,CAACkD,UAAU,EAAC,wBACzC;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ9F,OAAA;UAAK+F,KAAK,EAAE;YAAEK,UAAU,EAAE,iBAAiB;YAAEC,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,MAAM;YAAEiC,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,gBACrG1F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACX,QAAQ,CAAC0G,cAAc,CAAC,CAAC,EAAC,OAAK;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtF9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACP,SAAS;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClE9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjF,aAAa,CAACN,UAAU;UAAA;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/E,IAAI;UAAA;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACN9F,OAAA;UACEuH,OAAO,EAAEA,CAAA,KAAM8F,MAAM,CAAClC,QAAQ,CAACmC,MAAM,CAAC,CAAE;UACxCvH,KAAK,EAAE;YACLM,OAAO,EAAE,WAAW;YACpBmC,QAAQ,EAAE,QAAQ;YAClBpC,UAAU,EAAE,SAAS;YACrB4B,KAAK,EAAE,MAAM;YACbV,MAAM,EAAE,MAAM;YACdhB,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE;UACV,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACE9F,OAAA;IAAK+F,KAAK,EAAE;MAAEK,UAAU,EAAE,SAAS;MAAEuG,SAAS,EAAE,OAAO;MAAE3E,KAAK,EAAE;IAAO,CAAE;IAAAtC,QAAA,gBAEvE1F,OAAA;MAAK+F,KAAK,EAAE;QACVK,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,WAAW;QACpBkH,YAAY,EAAE,mBAAmB;QACjCvH,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,eAAe;QAC/B4B,UAAU,EAAE;MACd,CAAE;MAAAvF,QAAA,gBACA1F,OAAA;QAAA0F,QAAA,gBACE1F,OAAA;UAAI+F,KAAK,EAAE;YAAEwC,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAA9C,QAAA,GAAC,eAAG,EAAC7E,aAAa,CAAC8C,IAAI;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1E9F,OAAA;UAAG+F,KAAK,EAAE;YAAEwC,MAAM,EAAE,CAAC;YAAEiC,OAAO,EAAE;UAAI,CAAE;UAAA9E,QAAA,GAAE7E,aAAa,CAACkD,UAAU,EAAC,UAAG,EAAClD,aAAa,CAACuM,eAAe,CAACpE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAA;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtH,CAAC,eAEN9F,OAAA;QAAK+F,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAE+E,UAAU,EAAE;QAAS,CAAE;QAAAvF,QAAA,gBACjE1F,OAAA;UAAK+F,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClC1F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAhF,QAAA,GAAC,eAAG,EAAC7E,aAAa,CAACX,QAAQ,CAAC0G,cAAc,CAAC,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1G9F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACN9F,OAAA;UAAK+F,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClC1F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAhF,QAAA,GAAC,SAAE,EAAC7E,aAAa,CAACV,QAAQ;UAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxF9F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACN9F,OAAA;UAAK+F,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClC1F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAhF,QAAA,GAAC,qBAAI,EAAC7E,aAAa,CAACP,SAAS;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3F9F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACN9F,OAAA;UAAK+F,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClC1F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAhF,QAAA,GAAC,eAAG,EAAC7E,aAAa,CAACN,UAAU;UAAA;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3F9F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACN9F,OAAA;UAAK+F,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClC1F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAhF,QAAA,GAAC,eAAG,EAAC3E,IAAI;UAAA;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvE9F,OAAA;YAAK+F,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzD,aAAa,CAACc,MAAM,GAAG,CAAC,iBACvBnD,OAAA;MAAK+F,KAAK,EAAE;QAAEM,OAAO,EAAE;MAAY,CAAE;MAAAX,QAAA,EAClCrD,aAAa,CAAC8E,GAAG,CAAChD,YAAY,iBAC7BnE,OAAA;QAEE+F,KAAK,EAAE;UACLK,UAAU,EAAEjC,YAAY,CAACD,IAAI,KAAK,OAAO,GAAG,SAAS,GAC1CC,YAAY,CAACD,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;UAClEmC,OAAO,EAAE,UAAU;UACnBkC,MAAM,EAAE,OAAO;UACfjC,YAAY,EAAE,KAAK;UACnBkC,QAAQ,EAAE,QAAQ;UAClBxC,OAAO,EAAE,MAAM;UACfqD,cAAc,EAAE;QAClB,CAAE;QAAA3D,QAAA,gBAEF1F,OAAA;UAAA0F,QAAA,EAAOvB,YAAY,CAACF;QAAI;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChC9F,OAAA;UAAM+F,KAAK,EAAE;YAAEyE,OAAO,EAAE;UAAI,CAAE;UAAA9E,QAAA,EAAEvB,YAAY,CAACI;QAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAbzD3B,YAAY,CAACC,EAAE;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcjB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGD9F,OAAA;MAAK+F,KAAK,EAAE;QACVK,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,QAAQ;QACjBkH,YAAY,EAAE;MAChB,CAAE;MAAA7H,QAAA,EACC,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,CAACyB,GAAG,CAACqG,GAAG,iBACnFxN,OAAA;QAEEuH,OAAO,EAAEA,CAAA,KAAMvF,YAAY,CAACwL,GAAG,CAAE;QACjCzH,KAAK,EAAE;UACLK,UAAU,EAAErE,SAAS,KAAKyL,GAAG,GAAG,SAAS,GAAG,aAAa;UACzDxF,KAAK,EAAE,MAAM;UACbV,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,WAAW;UACpBkC,MAAM,EAAE,OAAO;UACflB,MAAM,EAAE,SAAS;UACjBf,YAAY,EAAE,aAAa;UAC3BwC,aAAa,EAAE,YAAY;UAC3BN,QAAQ,EAAE;QACZ,CAAE;QAAA9C,QAAA,GAED8H,GAAG,KAAK,UAAU,IAAI,KAAK,EAC3BA,GAAG,KAAK,YAAY,IAAI,IAAI,EAC5BA,GAAG,KAAK,SAAS,IAAI,IAAI,EACzBA,GAAG,KAAK,UAAU,IAAI,IAAI,EAC1BA,GAAG,KAAK,WAAW,IAAI,IAAI,EAC3BA,GAAG,KAAK,YAAY,IAAI,IAAI,EAC5B,GAAG,GAAGA,GAAG;MAAA,GApBLA,GAAG;QAAA7H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBF,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9F,OAAA;MAAK+F,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAE0C,MAAM,EAAE;MAAsB,CAAE;MAAAhD,QAAA,gBAE7D1F,OAAA;QAAK+F,KAAK,EAAE;UAAE0H,IAAI,EAAE,GAAG;UAAErH,UAAU,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAX,QAAA,gBAChE1F,OAAA,CAACF,QAAQ;UACP8M,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;UAC1BC,eAAe,EAAEhM,aAAc;UAC/BH,SAAS,EAAEA,SAAU;UACrBgN,gBAAgB,EAAE5L,mBAAoB;UACtCD,gBAAgB,EAAEA;QAAiB;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAEDjE,gBAAgB,iBACf7B,OAAA;UAAK+F,KAAK,EAAE;YACVK,UAAU,EAAE,SAAS;YACrBC,OAAO,EAAE,MAAM;YACfkC,MAAM,EAAE,QAAQ;YAChBjC,YAAY,EAAE,KAAK;YACnBqH,SAAS,EAAE,OAAO;YAClBC,SAAS,EAAE;UACb,CAAE;UAAAlI,QAAA,gBACA1F,OAAA;YAAA0F,QAAA,EAAK7D,gBAAgB,CAAC8B;UAAI;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChC9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjE,gBAAgB,CAACqF,KAAK;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvD9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjE,gBAAgB,CAAC2F,WAAW,CAAChB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9E9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjE,gBAAgB,CAAC4F,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC,CAACjB,cAAc,CAAC,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjI9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjE,gBAAgB,CAACgM,OAAO;UAAA;YAAAlI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D9F,OAAA;YAAA0F,QAAA,gBAAG1F,OAAA;cAAA0F,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjE,gBAAgB,CAAC4G,MAAM,CAACjC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN9F,OAAA;QAAK+F,KAAK,EAAE;UAAE0H,IAAI,EAAE,GAAG;UAAErH,UAAU,EAAE,SAAS;UAAEC,OAAO,EAAE,MAAM;UAAEuH,SAAS,EAAE;QAAO,CAAE;QAAAlI,QAAA,EAClFD,gBAAgB,CAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9F,OAAA;MAAK+F,KAAK,EAAE;QACVK,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,WAAW;QACpByH,SAAS,EAAE,mBAAmB;QAC9B9H,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,eAAe;QAC/B4B,UAAU,EAAE;MACd,CAAE;MAAAvF,QAAA,gBACA1F,OAAA;QAAA0F,QAAA,EACGzE,OAAO,iBACNjB,OAAA;UAAM+F,KAAK,EAAE;YAAEiC,KAAK,EAAE,SAAS;YAAEQ,QAAQ,EAAE;UAAO,CAAE;UAAA9C,QAAA,EAAEzE;QAAO;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACrE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN9F,OAAA;QACEuH,OAAO,EAAE5C,WAAY;QACrB+E,QAAQ,EAAEzH,WAAY;QACtB8D,KAAK,EAAE;UACLK,UAAU,EAAEnE,WAAW,GAAG,MAAM,GAAG,SAAS;UAC5C+F,KAAK,EAAE,MAAM;UACbV,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,WAAW;UACpBmC,QAAQ,EAAE,QAAQ;UAClBlC,YAAY,EAAE,KAAK;UACnBe,MAAM,EAAEpF,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CyI,UAAU,EAAE;QACd,CAAE;QAAAhF,QAAA,EAEDzD,WAAW,GAAG,iBAAiB,GAAG;MAAa;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrF,EAAA,CA/2CuBD,GAAG;AAAAuN,EAAA,GAAHvN,GAAG;AAAA,IAAAuN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}