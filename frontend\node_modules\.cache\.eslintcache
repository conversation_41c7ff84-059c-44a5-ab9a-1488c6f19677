[{"C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\src\\index.jsx": "1", "C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\src\\WorldMap.jsx": "3"}, {"size": 186, "mtime": 1750531102683, "results": "4", "hashOfConfig": "5"}, {"size": 121919, "mtime": 1750550702578, "results": "6", "hashOfConfig": "5"}, {"size": 5235, "mtime": 1750534515151, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bosjre", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\src\\App.jsx", ["17"], [], "C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\src\\WorldMap.jsx", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "18", "line": 2065, "column": 8, "nodeType": null}, "Parsing error: Identifier 'recruitUnit' has already been declared. (2065:8)"]